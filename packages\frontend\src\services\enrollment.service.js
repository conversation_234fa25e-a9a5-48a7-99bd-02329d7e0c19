import api from './api';

/**
 * Servicio para gestión de inscripciones del LMS
 */

// Inscribirse a un curso
export const enrollInCourse = async (courseId, userId = null) => {
  try {
    const data = userId ? { userId } : {};
    const response = await api.post(`/enrollments/course/${courseId}`, data);
    return response.data;
  } catch (error) {
    throw error.response?.data || error;
  }
};

// Obtener inscripciones de un usuario
export const getUserEnrollments = async (userId = null) => {
  try {
    const endpoint = userId ? `/enrollments/user/${userId}` : '/enrollments/user/';
    const response = await api.get(endpoint);
    return response.data;
  } catch (error) {
    throw error.response?.data || error;
  }
};

// Obtener inscripciones de un curso
export const getCourseEnrollments = async (courseId, params = {}) => {
  try {
    const response = await api.get(`/enrollments/course/${courseId}/students`, { params });
    return response.data;
  } catch (error) {
    throw error.response?.data || error;
  }
};

// Actualizar progreso de inscripción
export const updateEnrollmentProgress = async (enrollmentId, progressData) => {
  try {
    const response = await api.put(`/enrollments/${enrollmentId}/progress`, progressData);
    return response.data;
  } catch (error) {
    throw error.response?.data || error;
  }
};

// Cancelar inscripción
export const cancelEnrollment = async (enrollmentId) => {
  try {
    const response = await api.delete(`/enrollments/${enrollmentId}`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error;
  }
};

// Obtener progreso de un usuario en un curso específico
export const getUserCourseProgress = async (courseId) => {
  try {
    const enrollments = await getUserEnrollments();
    const courseEnrollment = enrollments.data.find(e => e.courseId === courseId);
    return courseEnrollment || null;
  } catch (error) {
    throw error.response?.data || error;
  }
};

// Marcar lección como completada
export const markLessonComplete = async (enrollmentId, lessonId, timeSpent = 0) => {
  try {
    const progressData = {
      currentLessonId: lessonId,
      timeSpent
    };
    return await updateEnrollmentProgress(enrollmentId, progressData);
  } catch (error) {
    throw error.response?.data || error;
  }
};

// Obtener estadísticas de inscripciones
export const getEnrollmentStats = async (courseId) => {
  try {
    const response = await getCourseEnrollments(courseId);
    const enrollments = response.data;
    
    const stats = {
      total: enrollments.length,
      enrolled: enrollments.filter(e => e.status === 'enrolled').length,
      inProgress: enrollments.filter(e => e.status === 'in_progress').length,
      completed: enrollments.filter(e => e.status === 'completed').length,
      dropped: enrollments.filter(e => e.status === 'dropped').length
    };
    
    return { success: true, data: stats };
  } catch (error) {
    throw error.response?.data || error;
  }
};

export default {
  enrollInCourse,
  getUserEnrollments,
  getCourseEnrollments,
  updateEnrollmentProgress,
  cancelEnrollment,
  getUserCourseProgress,
  markLessonComplete,
  getEnrollmentStats
};
