// Cargar variables de entorno
require('dotenv').config();

const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const cookieParser = require('cookie-parser');
const path = require('path');
const { sequelize } = require('./models');
const { initializeDatabase } = require('./utils/initDb');
const { verifyGlobalAdmin } = require('./utils/verifyAdmin');
const { debugMiddleware, debugLog, DEBUG_MODE } = require('./utils/debug');
const authRoutes = require('./routes/auth.routes');
const userRoutes = require('./routes/user.routes');
const docsRoutes = require('./routes/docs.routes');
const acceleratorRoutes = require('./routes/accelerator.routes');
const registrationCodeRoutes = require('./routes/registrationCode.routes');
const formRoutes = require('./routes/form.routes');
const applicationRoutes = require('./routes/application.routes');
const funnelStageRoutes = require('./routes/funnelStage.routes');
const sessionRoutes = require('./routes/session.routes');
const sessionNoteRoutes = require('./routes/sessionNote.routes');
const taskRoutes = require('./routes/task.routes');

// LMS Routes
const courseRoutes = require('./routes/course.routes');
const quizRoutes = require('./routes/quiz.routes');
const enrollmentRoutes = require('./routes/enrollment.routes');

// Inicializar app
const app = express();
const PORT = process.env.PORT || 5000;

// Mostrar información de configuración
debugLog('Modo de depuración activado');
debugLog('Configuración:', {
  port: PORT,
  nodeEnv: process.env.NODE_ENV,
  debugMode: DEBUG_MODE
});

// Middleware
app.use(cors());
app.use(express.json());
app.use(cookieParser());
app.use(morgan('dev'));
app.use(express.static(path.join(__dirname, '../public')));

// Middleware de depuración
if (DEBUG_MODE) {
  app.use(debugMiddleware);
}

// Rutas
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/docs', docsRoutes);
app.use('/api/accelerators', acceleratorRoutes);
app.use('/api/registration-codes', registrationCodeRoutes);
app.use('/api/forms', formRoutes);
app.use('/api/applications', applicationRoutes);
app.use('/api/funnel-stages', funnelStageRoutes);
app.use('/api/sessions', sessionRoutes);
app.use('/api/session-notes', sessionNoteRoutes);
app.use('/api/tasks', taskRoutes);

// LMS Routes
app.use('/api/courses', courseRoutes);
app.use('/api/quizzes', quizRoutes);
app.use('/api/enrollments', enrollmentRoutes);

// Ruta de prueba
app.get('/', (req, res) => {
  res.json({ message: 'Bienvenido a la API de Bumeran' });
});

// Ruta para la documentación de la API
app.get('/api-docs', (req, res) => {
  res.sendFile(path.join(__dirname, '../public/api-docs.html'));
});

// Manejo de errores
app.use((err, req, res, next) => {
  const { debugError } = require('./utils/debug');
  debugError(err.stack);
  console.error('Error en la aplicación:', err.message);

  res.status(500).json({
    success: false,
    message: 'Error interno del servidor',
    error: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

// Verificar si se está ejecutando en modo de inicialización solamente
const isInitOnly = process.argv.includes('--init-only');

// Iniciar servidor
const startServer = async () => {
  try {
    // Inicializar la base de datos (roles y usuario admin)
    await initializeDatabase({ forceSync: isInitOnly });

    // Verificar y corregir el rol del administrador global
    await verifyGlobalAdmin();

    // Si es modo de inicialización solamente, no iniciar el servidor
    if (isInitOnly) {
      console.log('Base de datos inicializada correctamente en modo init-only');
      process.exit(0);
      return;
    }

    app.listen(PORT, () => {
      console.log(`Servidor corriendo en el puerto ${PORT}`);
    });
  } catch (error) {
    console.error('Error al iniciar el servidor:', error);

    if (isInitOnly) {
      console.error('Error en modo init-only, saliendo...');
      process.exit(1);
      return;
    }

    // Iniciar el servidor incluso si hay un error en la base de datos
    // para permitir que la aplicación funcione parcialmente
    console.log('Intentando iniciar el servidor a pesar del error...');
    app.listen(PORT, () => {
      console.log(`Servidor corriendo en el puerto ${PORT} (con errores de inicialización)`);
    });
  }
};

startServer();
