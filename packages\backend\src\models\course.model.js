const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * Modelo Course - Representa un curso en el LMS
 * 
 * Un curso es la unidad principal de aprendizaje que contiene módulos,
 * lecciones y puede tener quizzes asociados.
 */
const Course = sequelize.define('Course', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  title: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [3, 200],
    },
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  shortDescription: {
    type: DataTypes.STRING(500),
    allowNull: true,
  },
  thumbnail: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'URL de la imagen de portada del curso',
  },
  duration: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Duración estimada en minutos',
  },
  difficulty: {
    type: DataTypes.ENUM('beginner', 'intermediate', 'advanced'),
    allowNull: false,
    defaultValue: 'beginner',
  },
  category: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'Categoría del curso (ej: Marketing, Finanzas, Tecnología)',
  },
  tags: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Tags separados por comas para búsqueda',
  },
  isPublished: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
  },
  instructorId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Users',
      key: 'id',
    },
    comment: 'Usuario que creó/instruye el curso',
  },
  acceleratorId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Accelerators',
      key: 'id',
    },
    comment: 'Aceleradora a la que pertenece el curso',
  },
  enrollmentLimit: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Límite de inscripciones (null = ilimitado)',
  },
  startDate: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Fecha de inicio del curso',
  },
  endDate: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Fecha de finalización del curso',
  },
  certificateTemplate: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Plantilla HTML para el certificado',
  },
  passingScore: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 70,
    validate: {
      min: 0,
      max: 100,
    },
    comment: 'Puntuación mínima para aprobar el curso (%)',
  },
}, {
  timestamps: true,
  indexes: [
    {
      fields: ['acceleratorId'],
    },
    {
      fields: ['instructorId'],
    },
    {
      fields: ['isPublished', 'isActive'],
    },
    {
      fields: ['category'],
    },
  ],
});

module.exports = Course;
