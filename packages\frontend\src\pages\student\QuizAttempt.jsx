import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { FiClock, FiCheckCircle, FiXCircle, FiArrowLeft, FiAlertCircle } from 'react-icons/fi';
import { getQuizById, startQuizAttempt, submitQuizAttempt } from '../../services/quiz.service';

const QuizAttempt = () => {
  const [quiz, setQuiz] = useState(null);
  const [attempt, setAttempt] = useState(null);
  const [answers, setAnswers] = useState({});
  const [timeLeft, setTimeLeft] = useState(null);
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [result, setResult] = useState(null);
  const [error, setError] = useState('');

  const { quizId } = useParams();
  const navigate = useNavigate();

  useEffect(() => {
    loadQuiz();
  }, [quizId]);

  useEffect(() => {
    if (timeLeft !== null && timeLeft > 0 && !result) {
      const timer = setTimeout(() => {
        setTimeLeft(timeLeft - 1);
      }, 1000);
      return () => clearTimeout(timer);
    } else if (timeLeft === 0 && !result) {
      // Tiempo agotado, enviar automáticamente
      handleSubmit();
    }
  }, [timeLeft, result]);

  const loadQuiz = async () => {
    try {
      setLoading(true);
      const response = await getQuizById(quizId);
      
      if (response.success) {
        setQuiz(response.data);
        await startAttempt();
      }
    } catch (err) {
      setError('Error al cargar quiz: ' + (err.message || 'Error desconocido'));
    } finally {
      setLoading(false);
    }
  };

  const startAttempt = async () => {
    try {
      const response = await startQuizAttempt(quizId);
      
      if (response.success) {
        setAttempt(response.data.attempt);
        setQuiz(response.data.quiz);
        
        // Configurar temporizador si hay límite de tiempo
        if (response.data.quiz.timeLimit) {
          setTimeLeft(response.data.quiz.timeLimit * 60); // Convertir minutos a segundos
        }
        
        // Inicializar respuestas vacías
        const initialAnswers = {};
        response.data.quiz.Questions.forEach(question => {
          initialAnswers[question.id] = [];
        });
        setAnswers(initialAnswers);
      }
    } catch (err) {
      setError('Error al iniciar quiz: ' + (err.message || 'Error desconocido'));
    }
  };

  const handleAnswerChange = (questionId, answerId, isMultiple = false) => {
    setAnswers(prev => {
      if (isMultiple) {
        // Para preguntas de opción múltiple
        const currentAnswers = prev[questionId] || [];
        if (currentAnswers.includes(answerId)) {
          return {
            ...prev,
            [questionId]: currentAnswers.filter(id => id !== answerId)
          };
        } else {
          return {
            ...prev,
            [questionId]: [...currentAnswers, answerId]
          };
        }
      } else {
        // Para preguntas de una sola respuesta
        return {
          ...prev,
          [questionId]: [answerId]
        };
      }
    });
  };

  const handleSubmit = async () => {
    try {
      setSubmitting(true);
      
      // Formatear respuestas para el backend
      const formattedAnswers = Object.entries(answers).map(([questionId, selectedAnswerIds]) => ({
        questionId: parseInt(questionId),
        selectedAnswerIds
      }));

      const response = await submitQuizAttempt(quizId, {
        attemptId: attempt.id,
        answers: formattedAnswers
      });

      if (response.success) {
        setResult(response.data);
      }
    } catch (err) {
      setError('Error al enviar quiz: ' + (err.message || 'Error desconocido'));
    } finally {
      setSubmitting(false);
    }
  };

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getProgressPercentage = () => {
    const answeredQuestions = Object.values(answers).filter(answer => answer.length > 0).length;
    return (answeredQuestions / (quiz?.Questions?.length || 1)) * 100;
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-2xl mx-auto">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          <div className="flex items-center gap-2">
            <FiXCircle className="w-5 h-5" />
            {error}
          </div>
        </div>
        <div className="mt-4">
          <button
            onClick={() => navigate(-1)}
            className="flex items-center gap-2 text-blue-600 hover:text-blue-800"
          >
            <FiArrowLeft className="w-4 h-4" />
            Volver
          </button>
        </div>
      </div>
    );
  }

  if (result) {
    return (
      <div className="max-w-2xl mx-auto space-y-6">
        {/* Resultado */}
        <div className="bg-white rounded-lg shadow-sm border p-6 text-center">
          <div className={`w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center ${
            result.passed ? 'bg-green-100' : 'bg-red-100'
          }`}>
            {result.passed ? (
              <FiCheckCircle className="w-8 h-8 text-green-600" />
            ) : (
              <FiXCircle className="w-8 h-8 text-red-600" />
            )}
          </div>
          
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            {result.passed ? '¡Felicitaciones!' : 'Intenta de nuevo'}
          </h2>
          
          <p className="text-gray-600 mb-6">
            {result.passed 
              ? 'Has aprobado el quiz exitosamente' 
              : 'No alcanzaste la puntuación mínima requerida'
            }
          </p>

          <div className="grid grid-cols-2 gap-4 mb-6">
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="text-2xl font-bold text-gray-900">{result.score.toFixed(1)}%</div>
              <div className="text-sm text-gray-600">Tu puntuación</div>
            </div>
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="text-2xl font-bold text-gray-900">{quiz.passingScore}%</div>
              <div className="text-sm text-gray-600">Puntuación mínima</div>
            </div>
          </div>

          <div className="text-sm text-gray-600 mb-6">
            Respondiste correctamente {result.totalPoints} de {result.maxPoints} preguntas
          </div>

          <div className="flex gap-4 justify-center">
            <button
              onClick={() => navigate(-1)}
              className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
            >
              Volver al Curso
            </button>
            {!result.passed && attempt.attemptNumber < quiz.maxAttempts && (
              <button
                onClick={() => window.location.reload()}
                className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg"
              >
                Intentar de Nuevo
              </button>
            )}
          </div>
        </div>
      </div>
    );
  }

  if (!quiz || !attempt) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-gray-600">Cargando quiz...</div>
      </div>
    );
  }

  const currentQuestionData = quiz.Questions[currentQuestion];

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{quiz.title}</h1>
            <p className="text-gray-600">Intento {attempt.attemptNumber} de {quiz.maxAttempts}</p>
          </div>
          
          {timeLeft !== null && (
            <div className={`flex items-center gap-2 px-4 py-2 rounded-lg ${
              timeLeft < 300 ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'
            }`}>
              <FiClock className="w-4 h-4" />
              <span className="font-mono font-bold">{formatTime(timeLeft)}</span>
            </div>
          )}
        </div>

        {/* Progreso */}
        <div className="mb-4">
          <div className="flex justify-between text-sm text-gray-600 mb-1">
            <span>Progreso del Quiz</span>
            <span>{getProgressPercentage().toFixed(0)}% completado</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${getProgressPercentage()}%` }}
            ></div>
          </div>
        </div>

        {/* Navegación de preguntas */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">
            Pregunta {currentQuestion + 1} de {quiz.Questions.length}
          </span>
          
          <div className="flex gap-2">
            <button
              onClick={() => setCurrentQuestion(Math.max(0, currentQuestion - 1))}
              disabled={currentQuestion === 0}
              className="px-3 py-1 text-sm border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              Anterior
            </button>
            <button
              onClick={() => setCurrentQuestion(Math.min(quiz.Questions.length - 1, currentQuestion + 1))}
              disabled={currentQuestion === quiz.Questions.length - 1}
              className="px-3 py-1 text-sm border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              Siguiente
            </button>
          </div>
        </div>
      </div>

      {/* Pregunta actual */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="mb-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-2">
            {currentQuestionData.question}
          </h2>
          {currentQuestionData.imageUrl && (
            <img
              src={currentQuestionData.imageUrl}
              alt="Imagen de la pregunta"
              className="max-w-full h-auto rounded-lg mb-4"
            />
          )}
          <p className="text-sm text-gray-600">
            Puntos: {currentQuestionData.points} | 
            Tipo: {currentQuestionData.type === 'multiple_choice' ? 'Opción múltiple' : 'Verdadero/Falso'}
          </p>
        </div>

        {/* Opciones de respuesta */}
        <div className="space-y-3">
          {currentQuestionData.Answers.map((answer) => {
            const isSelected = answers[currentQuestionData.id]?.includes(answer.id);
            
            return (
              <label
                key={answer.id}
                className={`flex items-center p-4 border rounded-lg cursor-pointer transition-colors ${
                  isSelected 
                    ? 'border-blue-500 bg-blue-50' 
                    : 'border-gray-300 hover:border-gray-400'
                }`}
              >
                <input
                  type={currentQuestionData.type === 'multiple_choice' ? 'checkbox' : 'radio'}
                  name={`question-${currentQuestionData.id}`}
                  checked={isSelected}
                  onChange={() => handleAnswerChange(
                    currentQuestionData.id, 
                    answer.id, 
                    currentQuestionData.type === 'multiple_choice'
                  )}
                  className="mr-3"
                />
                <span className="text-gray-900">{answer.answerText}</span>
              </label>
            );
          })}
        </div>
      </div>

      {/* Navegación inferior */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600">
            {Object.values(answers).filter(answer => answer.length > 0).length} de {quiz.Questions.length} preguntas respondidas
          </div>
          
          <div className="flex gap-4">
            <button
              onClick={() => navigate(-1)}
              className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
            >
              Cancelar
            </button>
            <button
              onClick={handleSubmit}
              disabled={submitting || Object.values(answers).filter(answer => answer.length > 0).length === 0}
              className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
            >
              {submitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Enviando...
                </>
              ) : (
                <>
                  <FiCheckCircle className="w-4 h-4" />
                  Enviar Quiz
                </>
              )}
            </button>
          </div>
        </div>
        
        {Object.values(answers).filter(answer => answer.length > 0).length === 0 && (
          <div className="mt-4 flex items-center gap-2 text-amber-600 text-sm">
            <FiAlertCircle className="w-4 h-4" />
            Debes responder al menos una pregunta antes de enviar
          </div>
        )}
      </div>
    </div>
  );
};

export default QuizAttempt;
