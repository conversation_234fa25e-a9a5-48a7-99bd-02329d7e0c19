const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * Modelo Certificate - Representa un certificado digital
 * 
 * Los certificados se generan automáticamente cuando un usuario
 * completa exitosamente un curso.
 */
const Certificate = sequelize.define('Certificate', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  certificateNumber: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    comment: 'Número único del certificado para verificación',
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Users',
      key: 'id',
    },
  },
  courseId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Courses',
      key: 'id',
    },
  },
  enrollmentId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Enrollments',
      key: 'id',
    },
  },
  issuedAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
  validUntil: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Fecha de expiración del certificado (si aplica)',
  },
  finalScore: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: false,
    comment: 'Puntuación final obtenida en el curso',
  },
  completionDate: {
    type: DataTypes.DATE,
    allowNull: false,
    comment: 'Fecha de finalización del curso',
  },
  certificateData: {
    type: DataTypes.TEXT,
    allowNull: false,
    comment: 'JSON con datos del certificado (nombre, curso, etc.)',
  },
  templateUsed: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Plantilla HTML utilizada para generar el certificado',
  },
  pdfUrl: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'URL del archivo PDF del certificado',
  },
  imageUrl: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'URL de la imagen del certificado',
  },
  verificationHash: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: 'Hash para verificación de autenticidad',
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
  },
  isRevoked: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  },
  revokedAt: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  revokedReason: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  revokedById: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'Users',
      key: 'id',
    },
  },
  downloadCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: 'Número de veces que se ha descargado',
  },
  lastDownloadedAt: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  metadata: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'JSON con metadatos adicionales',
  },
}, {
  timestamps: true,
  indexes: [
    {
      fields: ['certificateNumber'],
      unique: true,
    },
    {
      fields: ['userId'],
    },
    {
      fields: ['courseId'],
    },
    {
      fields: ['enrollmentId'],
    },
    {
      fields: ['verificationHash'],
    },
    {
      fields: ['isActive', 'isRevoked'],
    },
    {
      fields: ['issuedAt'],
    },
  ],
});

module.exports = Certificate;
