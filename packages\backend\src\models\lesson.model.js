const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * <PERSON><PERSON> Lesson - Representa una lección dentro de un módulo
 * 
 * Las lecciones son las unidades básicas de contenido educativo.
 * Pueden ser de diferentes tipos: video, texto, quiz, etc.
 */
const Lesson = sequelize.define('Lesson', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  title: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [3, 200],
    },
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  moduleId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Modules',
      key: 'id',
    },
  },
  orderIndex: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: 'Orden de la lección dentro del módulo',
  },
  type: {
    type: DataTypes.ENUM('video', 'text', 'quiz', 'document', 'interactive'),
    allowNull: false,
    defaultValue: 'text',
  },
  content: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Contenido de la lección (HTML, markdown, etc.)',
  },
  videoUrl: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'URL del video (YouTube, Vimeo, etc.)',
  },
  documentUrl: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'URL del documento (PDF, etc.)',
  },
  duration: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Duración estimada en minutos',
  },
  isPublished: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
  },
  isFree: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: 'Si la lección es gratuita (preview)',
  },
  prerequisites: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'JSON con IDs de lecciones prerequisitos',
  },
  resources: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'JSON con recursos adicionales (links, archivos)',
  },
  quizId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'Quizzes',
      key: 'id',
    },
    comment: 'Quiz asociado a la lección (si type = quiz)',
  },
}, {
  timestamps: true,
  indexes: [
    {
      fields: ['moduleId'],
    },
    {
      fields: ['moduleId', 'orderIndex'],
    },
    {
      fields: ['type'],
    },
    {
      fields: ['isPublished', 'isActive'],
    },
    {
      fields: ['quizId'],
    },
  ],
});

module.exports = Lesson;
