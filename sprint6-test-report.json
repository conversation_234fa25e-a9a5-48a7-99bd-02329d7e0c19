{"summary": {"totalTests": 14, "passed": 12, "failed": 1, "partial": 1, "successRate": 89.3, "duration": 0.261, "timestamp": "2025-05-24T22:19:09.236Z"}, "tests": [{"test": "Login de Administrador Global", "status": "PASS", "details": "Token obtenido correctamente", "timestamp": "2025-05-24T22:19:09.100Z"}, {"test": "Verificación de Rol GLOBAL_ADMIN", "status": "PASS", "details": "Usuario: <EMAIL>", "timestamp": "2025-05-24T22:19:09.109Z"}, {"test": "Obtener usuarios con rol MENTOR", "status": "PASS", "details": "2 usuarios encontrados", "timestamp": "2025-05-24T22:19:09.117Z"}, {"test": "Obtener usuarios con rol ENTREPRENEUR", "status": "PASS", "details": "4 usuarios encontrados", "timestamp": "2025-05-24T22:19:09.125Z"}, {"test": "Obtener usuarios con rol ACCELERATOR_ADMIN", "status": "PASS", "details": "1 usuarios encontrados", "timestamp": "2025-05-24T22:19:09.132Z"}, {"test": "Obtener lista de sesiones", "status": "PASS", "details": "2 sesiones encontradas", "timestamp": "2025-05-24T22:19:09.143Z"}, {"test": "Pruebas de sesiones", "status": "FAIL", "details": "El mentor ya tiene una sesión programada en ese horario", "timestamp": "2025-05-24T22:19:09.170Z"}, {"test": "Obtener lista de tareas", "status": "PASS", "details": "6 tareas encontradas", "timestamp": "2025-05-24T22:19:09.180Z"}, {"test": "Crear nueva tarea", "status": "PASS", "details": "Tarea ID: 7", "timestamp": "2025-05-24T22:19:09.203Z"}, {"test": "Obtener detalles de tarea", "status": "PASS", "details": "Detalles obtenidos correctamente", "timestamp": "2025-05-24T22:19:09.213Z"}, {"test": "Obtener lista de notas", "status": "PASS", "details": "2 notas encontradas", "timestamp": "2025-05-24T22:19:09.224Z"}, {"test": "Crear nueva nota de sesión", "status": "PARTIAL", "details": "No hay sesión disponible para crear nota", "timestamp": "2025-05-24T22:19:09.226Z"}, {"test": "Acceso al Frontend", "status": "PASS", "details": "Frontend accesible en puerto 5173", "timestamp": "2025-05-24T22:19:09.234Z"}, {"test": "Rutas del <PERSON> 6", "status": "PASS", "details": "Todas las rutas implementadas: /admin/sessions/new, /admin/sessions/:id, /admin/tasks, /admin/session-notes", "timestamp": "2025-05-24T22:19:09.235Z"}]}