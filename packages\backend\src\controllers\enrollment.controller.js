const { 
  Enrollment, 
  Course, 
  User, 
  Module, 
  Lesson, 
  Progress,
  Certificate 
} = require('../models');
const { debugLog } = require('../utils/debug');

/**
 * Controlador para gestión de inscripciones del LMS
 */

// Inscribir usuario a un curso
const enrollInCourse = async (req, res) => {
  try {
    const { courseId } = req.params;
    const { userId } = req.body; // Opcional, para inscripción manual por admin
    
    const targetUserId = userId || req.user.id;
    debugLog(`Inscribiendo usuario ${targetUserId} al curso ${courseId}`);

    // Verificar que el curso existe
    const course = await Course.findByPk(courseId);
    if (!course) {
      return res.status(404).json({
        success: false,
        message: 'Curso no encontrado'
      });
    }

    // Verificar si el curso está publicado
    if (!course.isPublished) {
      return res.status(400).json({
        success: false,
        message: 'El curso no está disponible para inscripción'
      });
    }

    // Verificar si ya está inscrito
    const existingEnrollment = await Enrollment.findOne({
      where: {
        courseId,
        userId: targetUserId
      }
    });

    if (existingEnrollment) {
      return res.status(400).json({
        success: false,
        message: 'El usuario ya está inscrito en este curso'
      });
    }

    // Verificar límite de inscripciones
    if (course.enrollmentLimit) {
      const currentEnrollments = await Enrollment.count({
        where: {
          courseId,
          status: ['enrolled', 'in_progress', 'completed']
        }
      });

      if (currentEnrollments >= course.enrollmentLimit) {
        return res.status(400).json({
          success: false,
          message: 'El curso ha alcanzado su límite de inscripciones'
        });
      }
    }

    // Crear inscripción
    const enrollment = await Enrollment.create({
      courseId,
      userId: targetUserId,
      enrolledAt: new Date(),
      status: 'enrolled',
      enrolledById: req.user.id !== targetUserId ? req.user.id : null
    });

    // Obtener inscripción con datos relacionados
    const enrollmentWithData = await Enrollment.findByPk(enrollment.id, {
      include: [
        {
          model: Course,
          attributes: ['id', 'title', 'description', 'thumbnail']
        },
        {
          model: User,
          attributes: ['id', 'firstName', 'lastName', 'email']
        }
      ]
    });

    res.status(201).json({
      success: true,
      message: 'Inscripción realizada exitosamente',
      data: enrollmentWithData
    });

  } catch (error) {
    debugLog('Error al inscribir en curso:', error);
    res.status(500).json({
      success: false,
      message: 'Error al inscribir en curso',
      error: error.message
    });
  }
};

// Obtener inscripciones de un usuario
const getUserEnrollments = async (req, res) => {
  try {
    const { userId } = req.params;
    const targetUserId = userId || req.user.id;
    
    debugLog(`Obteniendo inscripciones del usuario ${targetUserId}`);

    // Verificar permisos
    if (req.user.id !== parseInt(targetUserId) && req.user.role === 'ENTREPRENEUR') {
      return res.status(403).json({
        success: false,
        message: 'No tienes permisos para ver estas inscripciones'
      });
    }

    const enrollments = await Enrollment.findAll({
      where: { userId: targetUserId },
      include: [
        {
          model: Course,
          include: [
            {
              model: Module,
              as: 'Modules',
              attributes: ['id', 'title', 'orderIndex'],
              include: [
                {
                  model: Lesson,
                  as: 'Lessons',
                  attributes: ['id', 'title', 'type', 'duration']
                }
              ]
            }
          ]
        },
        {
          model: Module,
          as: 'CurrentModule',
          attributes: ['id', 'title']
        },
        {
          model: Lesson,
          as: 'CurrentLesson',
          attributes: ['id', 'title']
        },
        {
          model: Certificate,
          as: 'Certificate',
          attributes: ['id', 'certificateNumber', 'issuedAt', 'finalScore']
        }
      ],
      order: [['enrolledAt', 'DESC']]
    });

    res.json({
      success: true,
      data: enrollments
    });

  } catch (error) {
    debugLog('Error al obtener inscripciones:', error);
    res.status(500).json({
      success: false,
      message: 'Error al obtener inscripciones',
      error: error.message
    });
  }
};

// Obtener inscripciones de un curso
const getCourseEnrollments = async (req, res) => {
  try {
    const { courseId } = req.params;
    const { page = 1, limit = 10, status } = req.query;
    
    debugLog(`Obteniendo inscripciones del curso ${courseId}`);

    const offset = (page - 1) * limit;
    const where = { courseId };
    
    if (status) where.status = status;

    const enrollments = await Enrollment.findAndCountAll({
      where,
      include: [
        {
          model: User,
          attributes: ['id', 'firstName', 'lastName', 'email', 'profilePicture']
        },
        {
          model: Course,
          attributes: ['id', 'title']
        }
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['enrolledAt', 'DESC']]
    });

    res.json({
      success: true,
      data: enrollments.rows,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: enrollments.count,
        pages: Math.ceil(enrollments.count / limit)
      }
    });

  } catch (error) {
    debugLog('Error al obtener inscripciones del curso:', error);
    res.status(500).json({
      success: false,
      message: 'Error al obtener inscripciones del curso',
      error: error.message
    });
  }
};

// Actualizar progreso de inscripción
const updateEnrollmentProgress = async (req, res) => {
  try {
    const { enrollmentId } = req.params;
    const { 
      currentModuleId, 
      currentLessonId, 
      progressPercentage,
      timeSpent 
    } = req.body;
    
    debugLog(`Actualizando progreso de inscripción ${enrollmentId}`);

    const enrollment = await Enrollment.findByPk(enrollmentId);
    if (!enrollment) {
      return res.status(404).json({
        success: false,
        message: 'Inscripción no encontrada'
      });
    }

    // Verificar permisos
    if (enrollment.userId !== req.user.id && req.user.role === 'ENTREPRENEUR') {
      return res.status(403).json({
        success: false,
        message: 'No tienes permisos para actualizar esta inscripción'
      });
    }

    // Actualizar inscripción
    const updateData = {
      lastAccessedAt: new Date()
    };

    if (currentModuleId) updateData.currentModuleId = currentModuleId;
    if (currentLessonId) updateData.currentLessonId = currentLessonId;
    if (progressPercentage !== undefined) {
      updateData.progressPercentage = progressPercentage;
      
      // Cambiar estado si es necesario
      if (progressPercentage > 0 && enrollment.status === 'enrolled') {
        updateData.status = 'in_progress';
        updateData.startedAt = new Date();
      } else if (progressPercentage >= 100 && enrollment.status !== 'completed') {
        updateData.status = 'completed';
        updateData.completedAt = new Date();
      }
    }
    
    if (timeSpent) {
      updateData.totalTimeSpent = (enrollment.totalTimeSpent || 0) + timeSpent;
    }

    await enrollment.update(updateData);

    res.json({
      success: true,
      message: 'Progreso actualizado exitosamente',
      data: enrollment
    });

  } catch (error) {
    debugLog('Error al actualizar progreso:', error);
    res.status(500).json({
      success: false,
      message: 'Error al actualizar progreso',
      error: error.message
    });
  }
};

// Cancelar inscripción
const cancelEnrollment = async (req, res) => {
  try {
    const { enrollmentId } = req.params;
    debugLog(`Cancelando inscripción ${enrollmentId}`);

    const enrollment = await Enrollment.findByPk(enrollmentId);
    if (!enrollment) {
      return res.status(404).json({
        success: false,
        message: 'Inscripción no encontrada'
      });
    }

    // Verificar permisos
    if (enrollment.userId !== req.user.id && 
        !['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN'].includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: 'No tienes permisos para cancelar esta inscripción'
      });
    }

    // No permitir cancelar si ya está completado
    if (enrollment.status === 'completed') {
      return res.status(400).json({
        success: false,
        message: 'No se puede cancelar una inscripción completada'
      });
    }

    await enrollment.update({
      status: 'dropped',
      notes: `Cancelado por ${req.user.firstName} ${req.user.lastName} el ${new Date().toISOString()}`
    });

    res.json({
      success: true,
      message: 'Inscripción cancelada exitosamente'
    });

  } catch (error) {
    debugLog('Error al cancelar inscripción:', error);
    res.status(500).json({
      success: false,
      message: 'Error al cancelar inscripción',
      error: error.message
    });
  }
};

module.exports = {
  enrollInCourse,
  getUserEnrollments,
  getCourseEnrollments,
  updateEnrollmentProgress,
  cancelEnrollment
};
