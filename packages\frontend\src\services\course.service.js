import api from './api';

/**
 * Servicio para gestión de cursos del LMS
 */

// Obtener todos los cursos
export const getCourses = async (params = {}) => {
  try {
    const response = await api.get('/courses', { params });
    return response.data;
  } catch (error) {
    throw error.response?.data || error;
  }
};

// Obtener un curso por ID
export const getCourseById = async (id) => {
  try {
    const response = await api.get(`/courses/${id}`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error;
  }
};

// Crear un nuevo curso
export const createCourse = async (courseData) => {
  try {
    const response = await api.post('/courses', courseData);
    return response.data;
  } catch (error) {
    throw error.response?.data || error;
  }
};

// Actualizar un curso
export const updateCourse = async (id, courseData) => {
  try {
    const response = await api.put(`/courses/${id}`, courseData);
    return response.data;
  } catch (error) {
    throw error.response?.data || error;
  }
};

// Eliminar un curso
export const deleteCourse = async (id) => {
  try {
    const response = await api.delete(`/courses/${id}`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error;
  }
};

// Obtener cursos por categoría
export const getCoursesByCategory = async (category) => {
  try {
    const response = await api.get('/courses', { 
      params: { category } 
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || error;
  }
};

// Obtener cursos por dificultad
export const getCoursesByDifficulty = async (difficulty) => {
  try {
    const response = await api.get('/courses', { 
      params: { difficulty } 
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || error;
  }
};

// Buscar cursos
export const searchCourses = async (searchTerm) => {
  try {
    const response = await api.get('/courses', { 
      params: { search: searchTerm } 
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || error;
  }
};

export default {
  getCourses,
  getCourseById,
  createCourse,
  updateCourse,
  deleteCourse,
  getCoursesByCategory,
  getCoursesByDifficulty,
  searchCourses
};
