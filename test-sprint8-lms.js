/**
 * Script de pruebas para Sprint 8: LMS (Learning Management System)
 *
 * Prueba todas las funcionalidades del LMS implementadas:
 * - Cursos
 * - Quizzes
 * - Inscripciones
 * - Progreso
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';
const ADMIN_EMAIL = '<EMAIL>';
const ADMIN_PASSWORD = 'admin123';

let adminToken = '';
let testCourseId = null;
let testQuizId = null;
let testEnrollmentId = null;

// Configurar axios para mostrar errores completos
axios.defaults.timeout = 10000;

const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bright: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ️ ${message}`, 'blue');
}

function logWarning(message) {
  log(`⚠️ ${message}`, 'yellow');
}

async function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Función para hacer login y obtener token
async function login() {
  try {
    const response = await axios.post(`${BASE_URL}/auth/login`, {
      email: ADMIN_EMAIL,
      password: ADMIN_PASSWORD
    });

    if (response.data.success && response.data.token) {
      adminToken = response.data.token;
      logSuccess('Login de Administrador Global: PASS');
      logInfo(`Token obtenido correctamente`);
      return true;
    } else {
      logError('Login falló: No se obtuvo token');
      return false;
    }
  } catch (error) {
    logError(`Login falló: ${error.response?.data?.message || error.message}`);
    return false;
  }
}

// Configurar headers de autenticación
function getAuthHeaders() {
  return {
    'Authorization': `Bearer ${adminToken}`,
    'Content-Type': 'application/json'
  };
}

// Pruebas de Cursos
async function testCourses() {
  log('\n🎓 === PRUEBAS DE CURSOS ===', 'cyan');

  try {
    // 1. Crear un curso
    const courseData = {
      title: 'Introducción al Emprendimiento',
      description: 'Curso básico sobre los fundamentos del emprendimiento y creación de startups.',
      shortDescription: 'Aprende los conceptos básicos del emprendimiento.',
      difficulty: 'beginner',
      category: 'Emprendimiento',
      tags: 'emprendimiento,startup,negocio',
      duration: 120,
      passingScore: 75
    };

    const createResponse = await axios.post(`${BASE_URL}/courses`, courseData, {
      headers: getAuthHeaders()
    });

    if (createResponse.data.success) {
      testCourseId = createResponse.data.data.id;
      logSuccess('Crear curso: PASS');
      logInfo(`Curso creado con ID: ${testCourseId}`);
    } else {
      logError('Crear curso: FAIL');
      return false;
    }

    // 2. Obtener lista de cursos
    const listResponse = await axios.get(`${BASE_URL}/courses`, {
      headers: getAuthHeaders()
    });

    if (listResponse.data.success && Array.isArray(listResponse.data.data)) {
      logSuccess('Obtener lista de cursos: PASS');
      logInfo(`${listResponse.data.data.length} cursos encontrados`);
    } else {
      logError('Obtener lista de cursos: FAIL');
    }

    // 3. Obtener curso por ID
    const getResponse = await axios.get(`${BASE_URL}/courses/${testCourseId}`, {
      headers: getAuthHeaders()
    });

    if (getResponse.data.success && getResponse.data.data.id === testCourseId) {
      logSuccess('Obtener curso por ID: PASS');
      logInfo(`Curso obtenido: ${getResponse.data.data.title}`);
    } else {
      logError('Obtener curso por ID: FAIL');
    }

    // 4. Actualizar curso
    const updateData = {
      title: 'Introducción al Emprendimiento - Actualizado',
      isPublished: true
    };

    const updateResponse = await axios.put(`${BASE_URL}/courses/${testCourseId}`, updateData, {
      headers: getAuthHeaders()
    });

    if (updateResponse.data.success) {
      logSuccess('Actualizar curso: PASS');
      logInfo('Curso actualizado y publicado');
    } else {
      logError('Actualizar curso: FAIL');
    }

    return true;

  } catch (error) {
    logError(`Error en pruebas de cursos: ${error.response?.data?.message || error.message}`);
    if (error.response?.data) {
      console.log('Detalles del error:', JSON.stringify(error.response.data, null, 2));
    }
    return false;
  }
}

// Pruebas de Quizzes
async function testQuizzes() {
  log('\n📝 === PRUEBAS DE QUIZZES ===', 'cyan');

  try {
    // 1. Crear un quiz
    const quizData = {
      title: 'Quiz de Emprendimiento Básico',
      description: 'Evaluación de conocimientos básicos sobre emprendimiento',
      instructions: 'Responde todas las preguntas. Tienes 30 minutos.',
      courseId: testCourseId,
      timeLimit: 30,
      maxAttempts: 3,
      passingScore: 70,
      questions: [
        {
          question: '¿Qué es un MVP?',
          type: 'multiple_choice',
          points: 2,
          answers: [
            { answerText: 'Minimum Viable Product', isCorrect: true },
            { answerText: 'Maximum Value Proposition', isCorrect: false },
            { answerText: 'Most Valuable Player', isCorrect: false },
            { answerText: 'Market Validation Process', isCorrect: false }
          ]
        },
        {
          question: '¿Cuál es el objetivo principal de un pitch?',
          type: 'multiple_choice',
          points: 2,
          answers: [
            { answerText: 'Conseguir financiamiento', isCorrect: false },
            { answerText: 'Presentar la idea de negocio', isCorrect: true },
            { answerText: 'Vender el producto', isCorrect: false },
            { answerText: 'Contratar empleados', isCorrect: false }
          ]
        }
      ]
    };

    const createResponse = await axios.post(`${BASE_URL}/quizzes`, quizData, {
      headers: getAuthHeaders()
    });

    if (createResponse.data.success) {
      testQuizId = createResponse.data.data.id;
      logSuccess('Crear quiz: PASS');
      logInfo(`Quiz creado con ID: ${testQuizId}`);
    } else {
      logError('Crear quiz: FAIL');
      return false;
    }

    // 2. Obtener lista de quizzes
    const listResponse = await axios.get(`${BASE_URL}/quizzes`, {
      headers: getAuthHeaders()
    });

    if (listResponse.data.success && Array.isArray(listResponse.data.data)) {
      logSuccess('Obtener lista de quizzes: PASS');
      logInfo(`${listResponse.data.data.length} quizzes encontrados`);
    } else {
      logError('Obtener lista de quizzes: FAIL');
    }

    // 3. Obtener quiz por ID
    const getResponse = await axios.get(`${BASE_URL}/quizzes/${testQuizId}`, {
      headers: getAuthHeaders()
    });

    if (getResponse.data.success && getResponse.data.data.id === testQuizId) {
      logSuccess('Obtener quiz por ID: PASS');
      logInfo(`Quiz obtenido: ${getResponse.data.data.title}`);
      logInfo(`Preguntas: ${getResponse.data.data.Questions?.length || 0}`);
    } else {
      logError('Obtener quiz por ID: FAIL');
    }

    return true;

  } catch (error) {
    logError(`Error en pruebas de quizzes: ${error.response?.data?.message || error.message}`);
    return false;
  }
}

// Pruebas de Inscripciones
async function testEnrollments() {
  log('\n📚 === PRUEBAS DE INSCRIPCIONES ===', 'cyan');

  try {
    // 1. Inscribirse a un curso
    const enrollResponse = await axios.post(`${BASE_URL}/enrollments/course/${testCourseId}`, {}, {
      headers: getAuthHeaders()
    });

    if (enrollResponse.data.success) {
      testEnrollmentId = enrollResponse.data.data.id;
      logSuccess('Inscripción a curso: PASS');
      logInfo(`Inscripción creada con ID: ${testEnrollmentId}`);
    } else {
      logError('Inscripción a curso: FAIL');
      return false;
    }

    // 2. Obtener inscripciones del usuario
    const userEnrollmentsResponse = await axios.get(`${BASE_URL}/enrollments/user/`, {
      headers: getAuthHeaders()
    });

    if (userEnrollmentsResponse.data.success && Array.isArray(userEnrollmentsResponse.data.data)) {
      logSuccess('Obtener inscripciones del usuario: PASS');
      logInfo(`${userEnrollmentsResponse.data.data.length} inscripciones encontradas`);
    } else {
      logError('Obtener inscripciones del usuario: FAIL');
    }

    // 3. Obtener inscripciones del curso
    const courseEnrollmentsResponse = await axios.get(`${BASE_URL}/enrollments/course/${testCourseId}/students`, {
      headers: getAuthHeaders()
    });

    if (courseEnrollmentsResponse.data.success && Array.isArray(courseEnrollmentsResponse.data.data)) {
      logSuccess('Obtener inscripciones del curso: PASS');
      logInfo(`${courseEnrollmentsResponse.data.data.length} estudiantes inscritos`);
    } else {
      logError('Obtener inscripciones del curso: FAIL');
    }

    // 4. Actualizar progreso
    const progressData = {
      progressPercentage: 25.5,
      timeSpent: 15
    };

    const progressResponse = await axios.put(`${BASE_URL}/enrollments/${testEnrollmentId}/progress`, progressData, {
      headers: getAuthHeaders()
    });

    if (progressResponse.data.success) {
      logSuccess('Actualizar progreso: PASS');
      logInfo('Progreso actualizado a 25.5%');
    } else {
      logError('Actualizar progreso: FAIL');
    }

    return true;

  } catch (error) {
    logError(`Error en pruebas de inscripciones: ${error.response?.data?.message || error.message}`);
    return false;
  }
}

// Pruebas de Quiz Attempts
async function testQuizAttempts() {
  log('\n🎯 === PRUEBAS DE INTENTOS DE QUIZ ===', 'cyan');

  try {
    // 1. Iniciar intento de quiz
    const startResponse = await axios.post(`${BASE_URL}/quizzes/${testQuizId}/attempt`, {}, {
      headers: getAuthHeaders()
    });

    if (startResponse.data.success) {
      logSuccess('Iniciar intento de quiz: PASS');
      logInfo(`Intento iniciado: ${startResponse.data.data.attempt.attemptNumber}`);

      const attemptId = startResponse.data.data.attempt.id;

      // 2. Enviar respuestas
      const answers = [
        {
          questionId: startResponse.data.data.quiz.Questions[0].id,
          selectedAnswerIds: [startResponse.data.data.quiz.Questions[0].Answers[0].id] // Primera respuesta (correcta)
        },
        {
          questionId: startResponse.data.data.quiz.Questions[1].id,
          selectedAnswerIds: [startResponse.data.data.quiz.Questions[1].Answers[1].id] // Segunda respuesta (correcta)
        }
      ];

      const submitResponse = await axios.post(`${BASE_URL}/quizzes/${testQuizId}/submit`, {
        attemptId,
        answers
      }, {
        headers: getAuthHeaders()
      });

      if (submitResponse.data.success) {
        logSuccess('Enviar respuestas de quiz: PASS');
        logInfo(`Puntuación: ${submitResponse.data.data.score}%`);
        logInfo(`Aprobado: ${submitResponse.data.data.passed ? 'Sí' : 'No'}`);
      } else {
        logError('Enviar respuestas de quiz: FAIL');
      }
    } else {
      logError('Iniciar intento de quiz: FAIL');
    }

    return true;

  } catch (error) {
    logError(`Error en pruebas de intentos de quiz: ${error.response?.data?.message || error.message}`);
    return false;
  }
}

// Función principal
async function runTests() {
  const startTime = Date.now();

  log('🚀 INICIANDO PRUEBAS DEL SPRINT 8: LMS', 'bright');
  log('=====================================', 'bright');

  let totalTests = 0;
  let passedTests = 0;
  let failedTests = 0;

  // Login
  log('\n🔐 === PRUEBAS DE AUTENTICACIÓN ===', 'magenta');
  const loginSuccess = await login();
  totalTests++;
  if (loginSuccess) passedTests++; else failedTests++;

  if (!loginSuccess) {
    logError('No se puede continuar sin autenticación');
    return;
  }

  // Pruebas de Cursos
  const coursesSuccess = await testCourses();
  totalTests++;
  if (coursesSuccess) passedTests++; else failedTests++;

  // Pruebas de Quizzes
  const quizzesSuccess = await testQuizzes();
  totalTests++;
  if (quizzesSuccess) passedTests++; else failedTests++;

  // Pruebas de Inscripciones
  const enrollmentsSuccess = await testEnrollments();
  totalTests++;
  if (enrollmentsSuccess) passedTests++; else failedTests++;

  // Pruebas de Quiz Attempts
  const attemptsSuccess = await testQuizAttempts();
  totalTests++;
  if (attemptsSuccess) passedTests++; else failedTests++;

  // Resumen final
  const endTime = Date.now();
  const duration = (endTime - startTime) / 1000;

  log('\n📊 === RESUMEN DE PRUEBAS SPRINT 8 ===', 'bright');
  log(`Tiempo total: ${duration.toFixed(3)} segundos`);
  logSuccess(`Pruebas exitosas: ${passedTests}`);
  if (failedTests > 0) logError(`Pruebas fallidas: ${failedTests}`);
  log(`🎯 Total de pruebas: ${totalTests}`);

  const successRate = ((passedTests / totalTests) * 100).toFixed(1);
  log(`📈 Tasa de éxito: ${successRate}%`);

  // Guardar reporte
  const report = {
    sprint: 8,
    module: 'LMS',
    timestamp: new Date().toISOString(),
    duration: duration,
    totalTests,
    passedTests,
    failedTests,
    successRate: parseFloat(successRate),
    testResults: {
      authentication: loginSuccess,
      courses: coursesSuccess,
      quizzes: quizzesSuccess,
      enrollments: enrollmentsSuccess,
      quizAttempts: attemptsSuccess
    },
    testData: {
      courseId: testCourseId,
      quizId: testQuizId,
      enrollmentId: testEnrollmentId
    }
  };

  require('fs').writeFileSync('sprint8-test-report.json', JSON.stringify(report, null, 2));
  log('\n📄 Reporte detallado guardado en: sprint8-test-report.json');

  if (successRate >= 90) {
    log('\n🎉 SPRINT 8 - ESTADO: EXCELENTE', 'green');
  } else if (successRate >= 80) {
    log('\n✅ SPRINT 8 - ESTADO: BUENO', 'yellow');
  } else if (successRate >= 70) {
    log('\n⚠️ SPRINT 8 - ESTADO: ACEPTABLE', 'yellow');
  } else {
    log('\n❌ SPRINT 8 - ESTADO: NECESITA MEJORAS', 'red');
  }
}

// Ejecutar pruebas
runTests().catch(error => {
  logError(`Error fatal en las pruebas: ${error.message}`);
  process.exit(1);
});
