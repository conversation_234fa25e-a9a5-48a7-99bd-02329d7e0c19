const {
  Course,
  Module,
  Lesson,
  Quiz,
  Enrollment,
  User,
  Accelerator,
  Progress
} = require('../models');
const { debugLog } = require('../utils/debug');

/**
 * Controlador para gestión de cursos del LMS
 */

// Obtener todos los cursos
const getCourses = async (req, res) => {
  try {
    debugLog('Obteniendo lista de cursos');

    const {
      page = 1,
      limit = 10,
      category,
      difficulty,
      isPublished,
      acceleratorId
    } = req.query;

    const offset = (page - 1) * limit;
    const where = {};

    // Filtros
    if (category) where.category = category;
    if (difficulty) where.difficulty = difficulty;
    if (isPublished !== undefined) where.isPublished = isPublished === 'true';
    if (acceleratorId) where.acceleratorId = acceleratorId;

    // Si no es admin global, filtrar por aceleradora del usuario
    if (req.user.role !== 'GLOBAL_ADMIN' && !acceleratorId) {
      // Para usuarios no admin, mostrar solo cursos de su aceleradora
      if (req.user.acceleratorId) {
        where.acceleratorId = req.user.acceleratorId;
      } else {
        // Si no tiene aceleradora asignada, usar la primera disponible
        const { Accelerator } = require('../models');
        const firstAccelerator = await Accelerator.findOne();
        where.acceleratorId = firstAccelerator ? firstAccelerator.id : 1;
      }
    }

    const courses = await Course.findAndCountAll({
      where,
      include: [
        {
          model: User,
          as: 'Instructor',
          attributes: ['id', 'firstName', 'lastName', 'email']
        },
        {
          model: Accelerator,
          attributes: ['id', 'name']
        },
        {
          model: Module,
          as: 'Modules',
          attributes: ['id', 'title', 'orderIndex'],
          include: [
            {
              model: Lesson,
              as: 'Lessons',
              attributes: ['id', 'title', 'type', 'duration']
            }
          ]
        },
        {
          model: Enrollment,
          as: 'Enrollments',
          attributes: ['id', 'status'],
          required: false
        }
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['createdAt', 'DESC']]
    });

    // Calcular estadísticas adicionales
    const coursesWithStats = courses.rows.map(course => {
      const courseData = course.toJSON();
      courseData.totalModules = courseData.Modules?.length || 0;
      courseData.totalLessons = courseData.Modules?.reduce((acc, module) =>
        acc + (module.Lessons?.length || 0), 0) || 0;
      courseData.totalEnrollments = courseData.Enrollments?.length || 0;
      return courseData;
    });

    res.json({
      success: true,
      data: coursesWithStats,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: courses.count,
        pages: Math.ceil(courses.count / limit)
      }
    });

  } catch (error) {
    debugLog('Error al obtener cursos:', error);
    res.status(500).json({
      success: false,
      message: 'Error al obtener cursos',
      error: error.message
    });
  }
};

// Obtener un curso por ID
const getCourseById = async (req, res) => {
  try {
    const { id } = req.params;
    debugLog(`Obteniendo curso con ID: ${id}`);

    const course = await Course.findByPk(id, {
      include: [
        {
          model: User,
          as: 'Instructor',
          attributes: ['id', 'firstName', 'lastName', 'email', 'profilePicture']
        },
        {
          model: Accelerator,
          attributes: ['id', 'name', 'description']
        },
        {
          model: Module,
          as: 'Modules',
          include: [
            {
              model: Lesson,
              as: 'Lessons',
              include: [
                {
                  model: Quiz,
                  as: 'Quiz',
                  attributes: ['id', 'title', 'timeLimit', 'maxAttempts']
                }
              ]
            }
          ],
          order: [['orderIndex', 'ASC']]
        },
        {
          model: Quiz,
          as: 'Quizzes',
          attributes: ['id', 'title', 'description', 'timeLimit']
        }
      ]
    });

    if (!course) {
      return res.status(404).json({
        success: false,
        message: 'Curso no encontrado'
      });
    }

    // Verificar permisos
    if (req.user.role !== 'GLOBAL_ADMIN' && course.acceleratorId !== req.user.acceleratorId) {
      return res.status(403).json({
        success: false,
        message: 'No tienes permisos para ver este curso'
      });
    }

    res.json({
      success: true,
      data: course
    });

  } catch (error) {
    debugLog('Error al obtener curso:', error);
    res.status(500).json({
      success: false,
      message: 'Error al obtener curso',
      error: error.message
    });
  }
};

// Crear un nuevo curso
const createCourse = async (req, res) => {
  try {
    debugLog('Creando nuevo curso');

    const {
      title,
      description,
      shortDescription,
      thumbnail,
      duration,
      difficulty,
      category,
      tags,
      acceleratorId,
      enrollmentLimit,
      startDate,
      endDate,
      passingScore
    } = req.body;

    // Validaciones básicas
    if (!title || !description) {
      return res.status(400).json({
        success: false,
        message: 'Título y descripción son requeridos'
      });
    }

    // Determinar acceleratorId
    let targetAcceleratorId = acceleratorId;

    // Si no se especifica acceleratorId, usar el del usuario o el primero disponible
    if (!targetAcceleratorId) {
      if (req.user.role === 'GLOBAL_ADMIN') {
        // Para admin global, usar el primer accelerator disponible o crear uno por defecto
        const { Accelerator } = require('../models');
        const firstAccelerator = await Accelerator.findOne();
        if (firstAccelerator) {
          targetAcceleratorId = firstAccelerator.id;
        } else {
          // Crear aceleradora por defecto si no existe ninguna
          const defaultAccelerator = await Accelerator.create({
            name: 'Aceleradora Principal',
            description: 'Aceleradora principal del sistema',
            website: 'https://bumeran.com',
            isActive: true
          });
          targetAcceleratorId = defaultAccelerator.id;
        }
      } else {
        // Para otros roles, usar su aceleradora asignada
        targetAcceleratorId = req.user.acceleratorId || 1; // Fallback a ID 1
      }
    }

    // Verificar permisos de aceleradora
    if (req.user.role !== 'GLOBAL_ADMIN' && targetAcceleratorId !== req.user.acceleratorId) {
      return res.status(403).json({
        success: false,
        message: 'No tienes permisos para crear cursos en esta aceleradora'
      });
    }

    const course = await Course.create({
      title,
      description,
      shortDescription,
      thumbnail,
      duration,
      difficulty: difficulty || 'beginner',
      category,
      tags,
      instructorId: req.user.id,
      acceleratorId: targetAcceleratorId,
      enrollmentLimit,
      startDate,
      endDate,
      passingScore: passingScore || 70
    });

    // Obtener el curso creado con relaciones
    const createdCourse = await Course.findByPk(course.id, {
      include: [
        {
          model: User,
          as: 'Instructor',
          attributes: ['id', 'firstName', 'lastName', 'email']
        },
        {
          model: Accelerator,
          attributes: ['id', 'name']
        }
      ]
    });

    res.status(201).json({
      success: true,
      message: 'Curso creado exitosamente',
      data: createdCourse
    });

  } catch (error) {
    debugLog('Error al crear curso:', error);
    res.status(500).json({
      success: false,
      message: 'Error al crear curso',
      error: error.message
    });
  }
};

// Actualizar un curso
const updateCourse = async (req, res) => {
  try {
    const { id } = req.params;
    debugLog(`Actualizando curso con ID: ${id}`);

    const course = await Course.findByPk(id);
    if (!course) {
      return res.status(404).json({
        success: false,
        message: 'Curso no encontrado'
      });
    }

    // Verificar permisos
    if (req.user.role !== 'GLOBAL_ADMIN' &&
        course.acceleratorId !== req.user.acceleratorId &&
        course.instructorId !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'No tienes permisos para editar este curso'
      });
    }

    const updatedCourse = await course.update(req.body);

    res.json({
      success: true,
      message: 'Curso actualizado exitosamente',
      data: updatedCourse
    });

  } catch (error) {
    debugLog('Error al actualizar curso:', error);
    res.status(500).json({
      success: false,
      message: 'Error al actualizar curso',
      error: error.message
    });
  }
};

// Eliminar un curso
const deleteCourse = async (req, res) => {
  try {
    const { id } = req.params;
    debugLog(`Eliminando curso con ID: ${id}`);

    const course = await Course.findByPk(id);
    if (!course) {
      return res.status(404).json({
        success: false,
        message: 'Curso no encontrado'
      });
    }

    // Verificar permisos
    if (req.user.role !== 'GLOBAL_ADMIN' &&
        course.acceleratorId !== req.user.acceleratorId &&
        course.instructorId !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'No tienes permisos para eliminar este curso'
      });
    }

    // Verificar si hay inscripciones activas
    const activeEnrollments = await Enrollment.count({
      where: {
        courseId: id,
        status: ['enrolled', 'in_progress']
      }
    });

    if (activeEnrollments > 0) {
      return res.status(400).json({
        success: false,
        message: 'No se puede eliminar un curso con inscripciones activas'
      });
    }

    await course.destroy();

    res.json({
      success: true,
      message: 'Curso eliminado exitosamente'
    });

  } catch (error) {
    debugLog('Error al eliminar curso:', error);
    res.status(500).json({
      success: false,
      message: 'Error al eliminar curso',
      error: error.message
    });
  }
};

module.exports = {
  getCourses,
  getCourseById,
  createCourse,
  updateCourse,
  deleteCourse
};
