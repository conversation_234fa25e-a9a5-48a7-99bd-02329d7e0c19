const { sequelize } = require('../config/database');
const User = require('./user.model');
const Role = require('./role.model');
const Accelerator = require('./accelerator.model');
const RegistrationCode = require('./registrationCode.model');
const Form = require('./form.model');
const Field = require('./field.model');
const Application = require('./application.model');
const FunnelStage = require('./funnelStage.model');
const Session = require('./session.model');
const SessionNote = require('./sessionNote.model');
const Task = require('./task.model');

// LMS Models
const Course = require('./course.model');
const Module = require('./module.model');
const Lesson = require('./lesson.model');
const Quiz = require('./quiz.model');
const QuizQuestion = require('./quizQuestion.model');
const QuizAnswer = require('./quizAnswer.model');
const QuizAttempt = require('./quizAttempt.model');
const Enrollment = require('./enrollment.model');
const Progress = require('./progress.model');
const Certificate = require('./certificate.model');

// Definir relaciones entre modelos
User.belongsTo(Role, { foreignKey: 'roleId' });
Role.hasMany(User, { foreignKey: 'roleId' });

// Relaciones para aceleradoras
// Un usuario puede ser administrador de una aceleradora
User.belongsToMany(Accelerator, { through: 'AcceleratorAdmins', as: 'managedAccelerators' });
Accelerator.belongsToMany(User, { through: 'AcceleratorAdmins', as: 'administrators' });

// Relaciones para códigos de registro
RegistrationCode.belongsTo(Role, { foreignKey: 'roleId' });
RegistrationCode.belongsTo(Accelerator, { foreignKey: 'acceleratorId', as: 'accelerator' });
RegistrationCode.belongsTo(User, { foreignKey: 'createdBy', as: 'creator' });

// Relaciones para formularios
Form.belongsTo(Accelerator, { foreignKey: 'acceleratorId' });
Form.belongsTo(User, { foreignKey: 'createdById', as: 'creator' });
Accelerator.hasMany(Form, { foreignKey: 'acceleratorId' });

// Relaciones para campos de formulario
Field.belongsTo(Form, { foreignKey: 'formId' });
Form.hasMany(Field, { foreignKey: 'formId' });

// Relaciones para solicitudes
Application.belongsTo(Form, { foreignKey: 'formId' });
Application.belongsTo(User, { foreignKey: 'applicantId', as: 'applicant' });
Application.belongsTo(Accelerator, { foreignKey: 'acceleratorId' });
Application.belongsTo(FunnelStage, { foreignKey: 'funnelStageId', as: 'stage' });
Form.hasMany(Application, { foreignKey: 'formId' });
User.hasMany(Application, { foreignKey: 'applicantId', as: 'applications' });
Accelerator.hasMany(Application, { foreignKey: 'acceleratorId' });

// Relaciones para etapas del embudo
FunnelStage.belongsTo(Accelerator, { foreignKey: 'acceleratorId' });
Accelerator.hasMany(FunnelStage, { foreignKey: 'acceleratorId', as: 'funnelStages' });
FunnelStage.hasMany(Application, { foreignKey: 'funnelStageId' });

// Coaching & Mentoría Session associations
User.hasMany(Session, { foreignKey: 'mentorId', as: 'MentoringSessions' });
User.hasMany(Session, { foreignKey: 'entrepreneurId', as: 'CoachingSessions' });
Session.belongsTo(User, { as: 'Mentor', foreignKey: 'mentorId' });
Session.belongsTo(User, { as: 'Entrepreneur', foreignKey: 'entrepreneurId' });
Accelerator.hasMany(Session, { foreignKey: 'acceleratorId' });
Session.belongsTo(Accelerator, { foreignKey: 'acceleratorId' });

// SessionNote associations
Session.hasMany(SessionNote, { foreignKey: 'sessionId', as: 'Notes' });
SessionNote.belongsTo(Session, { foreignKey: 'sessionId' });
User.hasMany(SessionNote, { foreignKey: 'authorId', as: 'AuthoredNotes' });
SessionNote.belongsTo(User, { as: 'Author', foreignKey: 'authorId' });

// Task associations
Session.hasMany(Task, { foreignKey: 'sessionId', as: 'Tasks' }); // Tasks originating from a session
Task.belongsTo(Session, { foreignKey: 'sessionId' });
User.hasMany(Task, { foreignKey: 'assignedToId', as: 'AssignedTasks' });
User.hasMany(Task, { foreignKey: 'assignedById', as: 'CreatedTasks' });
Task.belongsTo(User, { as: 'AssignedTo', foreignKey: 'assignedToId' });
Task.belongsTo(User, { as: 'AssignedBy', foreignKey: 'assignedById' });
Accelerator.hasMany(Task, { foreignKey: 'acceleratorId' });
Task.belongsTo(Accelerator, { foreignKey: 'acceleratorId' });

// LMS Relationships
// Course relationships
Course.belongsTo(User, { as: 'Instructor', foreignKey: 'instructorId' });
Course.belongsTo(Accelerator, { foreignKey: 'acceleratorId' });
User.hasMany(Course, { as: 'InstructedCourses', foreignKey: 'instructorId' });
Accelerator.hasMany(Course, { foreignKey: 'acceleratorId' });

// Module relationships
Module.belongsTo(Course, { foreignKey: 'courseId' });
Course.hasMany(Module, { foreignKey: 'courseId', as: 'Modules' });

// Lesson relationships
Lesson.belongsTo(Module, { foreignKey: 'moduleId' });
Lesson.belongsTo(Quiz, { foreignKey: 'quizId', as: 'Quiz' });
Module.hasMany(Lesson, { foreignKey: 'moduleId', as: 'Lessons' });

// Quiz relationships
Quiz.belongsTo(Course, { foreignKey: 'courseId' });
Quiz.belongsTo(Module, { foreignKey: 'moduleId' });
Quiz.belongsTo(User, { as: 'Creator', foreignKey: 'createdById' });
Course.hasMany(Quiz, { foreignKey: 'courseId', as: 'Quizzes' });
Module.hasMany(Quiz, { foreignKey: 'moduleId', as: 'Quizzes' });
User.hasMany(Quiz, { as: 'CreatedQuizzes', foreignKey: 'createdById' });

// Quiz Question relationships
QuizQuestion.belongsTo(Quiz, { foreignKey: 'quizId' });
Quiz.hasMany(QuizQuestion, { foreignKey: 'quizId', as: 'Questions' });

// Quiz Answer relationships
QuizAnswer.belongsTo(QuizQuestion, { foreignKey: 'questionId' });
QuizQuestion.hasMany(QuizAnswer, { foreignKey: 'questionId', as: 'Answers' });

// Quiz Attempt relationships
QuizAttempt.belongsTo(Quiz, { foreignKey: 'quizId' });
QuizAttempt.belongsTo(User, { foreignKey: 'userId' });
Quiz.hasMany(QuizAttempt, { foreignKey: 'quizId', as: 'Attempts' });
User.hasMany(QuizAttempt, { foreignKey: 'userId', as: 'QuizAttempts' });

// Enrollment relationships
Enrollment.belongsTo(Course, { foreignKey: 'courseId' });
Enrollment.belongsTo(User, { foreignKey: 'userId' });
Enrollment.belongsTo(Module, { as: 'CurrentModule', foreignKey: 'currentModuleId' });
Enrollment.belongsTo(Lesson, { as: 'CurrentLesson', foreignKey: 'currentLessonId' });
Enrollment.belongsTo(User, { as: 'EnrolledBy', foreignKey: 'enrolledById' });
Course.hasMany(Enrollment, { foreignKey: 'courseId', as: 'Enrollments' });
User.hasMany(Enrollment, { foreignKey: 'userId', as: 'Enrollments' });

// Progress relationships
Progress.belongsTo(User, { foreignKey: 'userId' });
Progress.belongsTo(Course, { foreignKey: 'courseId' });
Progress.belongsTo(Module, { foreignKey: 'moduleId' });
Progress.belongsTo(Lesson, { foreignKey: 'lessonId' });
User.hasMany(Progress, { foreignKey: 'userId', as: 'Progress' });
Course.hasMany(Progress, { foreignKey: 'courseId', as: 'Progress' });
Module.hasMany(Progress, { foreignKey: 'moduleId', as: 'Progress' });
Lesson.hasMany(Progress, { foreignKey: 'lessonId', as: 'Progress' });

// Certificate relationships
Certificate.belongsTo(User, { foreignKey: 'userId' });
Certificate.belongsTo(Course, { foreignKey: 'courseId' });
Certificate.belongsTo(Enrollment, { foreignKey: 'enrollmentId' });
Certificate.belongsTo(User, { as: 'RevokedBy', foreignKey: 'revokedById' });
User.hasMany(Certificate, { foreignKey: 'userId', as: 'Certificates' });
Course.hasMany(Certificate, { foreignKey: 'courseId', as: 'Certificates' });
Enrollment.hasOne(Certificate, { foreignKey: 'enrollmentId', as: 'Certificate' });

module.exports = {
  sequelize,
  User,
  Role,
  Accelerator,
  RegistrationCode,
  Form,
  Field,
  Application,
  FunnelStage,
  Session,
  SessionNote,
  Task,
  // LMS Models
  Course,
  Module,
  Lesson,
  Quiz,
  QuizQuestion,
  QuizAnswer,
  QuizAttempt,
  Enrollment,
  Progress,
  Certificate,
};
