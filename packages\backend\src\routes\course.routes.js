const express = require('express');
const router = express.Router();
const { verifyToken } = require('../middlewares/auth.middleware');
const {
  getCourses,
  getCourseById,
  createCourse,
  updateCourse,
  deleteCourse
} = require('../controllers/course.controller');

/**
 * Rutas para gestión de cursos del LMS
 * Todas las rutas requieren autenticación
 */

// Obtener todos los cursos
// GET /api/courses
router.get('/', verifyToken, getCourses);

// Obtener un curso específico
// GET /api/courses/:id
router.get('/:id', verifyToken, getCourseById);

// Crear un nuevo curso
// POST /api/courses
router.post('/', verifyToken, createCourse);

// Actualizar un curso
// PUT /api/courses/:id
router.put('/:id', verifyToken, updateCourse);

// Eliminar un curso
// DELETE /api/courses/:id
router.delete('/:id', verifyToken, deleteCourse);

module.exports = router;
