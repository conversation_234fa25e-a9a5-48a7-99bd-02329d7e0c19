const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * Modelo Quiz - Representa un cuestionario
 * 
 * Los quizzes pueden estar asociados a lecciones o ser independientes.
 * Contienen múltiples preguntas y tienen configuraciones de scoring.
 */
const Quiz = sequelize.define('Quiz', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  title: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [3, 200],
    },
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  instructions: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Instrucciones para el estudiante',
  },
  courseId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'Courses',
      key: 'id',
    },
    comment: 'Curso al que pertenece (opcional)',
  },
  moduleId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'Modules',
      key: 'id',
    },
    comment: '<PERSON>ó<PERSON><PERSON> al que pertenece (opcional)',
  },
  timeLimit: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Límite de tiempo en minutos (null = sin límite)',
  },
  maxAttempts: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 3,
    comment: 'Número máximo de intentos permitidos',
  },
  passingScore: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 70,
    validate: {
      min: 0,
      max: 100,
    },
    comment: 'Puntuación mínima para aprobar (%)',
  },
  shuffleQuestions: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: 'Si las preguntas se muestran en orden aleatorio',
  },
  shuffleAnswers: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: 'Si las respuestas se muestran en orden aleatorio',
  },
  showCorrectAnswers: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: 'Si mostrar respuestas correctas al finalizar',
  },
  showScoreImmediately: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: 'Si mostrar puntuación inmediatamente',
  },
  isPublished: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
  },
  createdById: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Users',
      key: 'id',
    },
    comment: 'Usuario que creó el quiz',
  },
}, {
  timestamps: true,
  indexes: [
    {
      fields: ['courseId'],
    },
    {
      fields: ['moduleId'],
    },
    {
      fields: ['createdById'],
    },
    {
      fields: ['isPublished', 'isActive'],
    },
  ],
});

module.exports = Quiz;
