const express = require('express');
const router = express.Router();
const { verifyToken } = require('../middlewares/auth.middleware');
const {
  getQuizzes,
  getQuizById,
  createQuiz,
  startQuizAttempt,
  submitQuizAttempt
} = require('../controllers/quiz.controller');

/**
 * Rutas para gestión de quizzes del LMS
 * Todas las rutas requieren autenticación
 */

// Obtener todos los quizzes
// GET /api/quizzes
router.get('/', verifyToken, getQuizzes);

// Obtener un quiz específico
// GET /api/quizzes/:id
router.get('/:id', verifyToken, getQuizById);

// Crear un nuevo quiz
// POST /api/quizzes
router.post('/', verifyToken, createQuiz);

// Iniciar un intento de quiz
// POST /api/quizzes/:id/attempt
router.post('/:id/attempt', verifyToken, startQuizAttempt);

// Enviar respuestas de quiz
// POST /api/quizzes/:id/submit
router.post('/:id/submit', verifyToken, submitQuizAttempt);

module.exports = router;
