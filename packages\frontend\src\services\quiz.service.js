import api from './api';

/**
 * Servicio para gestión de quizzes del LMS
 */

// Obtener todos los quizzes
export const getQuizzes = async (params = {}) => {
  try {
    const response = await api.get('/quizzes', { params });
    return response.data;
  } catch (error) {
    throw error.response?.data || error;
  }
};

// Obtener un quiz por ID
export const getQuizById = async (id) => {
  try {
    const response = await api.get(`/quizzes/${id}`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error;
  }
};

// Crear un nuevo quiz
export const createQuiz = async (quizData) => {
  try {
    const response = await api.post('/quizzes', quizData);
    return response.data;
  } catch (error) {
    throw error.response?.data || error;
  }
};

// Iniciar un intento de quiz
export const startQuizAttempt = async (quizId) => {
  try {
    const response = await api.post(`/quizzes/${quizId}/attempt`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error;
  }
};

// Enviar respuestas de quiz
export const submitQuizAttempt = async (quizId, attemptData) => {
  try {
    const response = await api.post(`/quizzes/${quizId}/submit`, attemptData);
    return response.data;
  } catch (error) {
    throw error.response?.data || error;
  }
};

// Obtener quizzes por curso
export const getQuizzesByCourse = async (courseId) => {
  try {
    const response = await api.get('/quizzes', { 
      params: { courseId } 
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || error;
  }
};

// Obtener quizzes por módulo
export const getQuizzesByModule = async (moduleId) => {
  try {
    const response = await api.get('/quizzes', { 
      params: { moduleId } 
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || error;
  }
};

export default {
  getQuizzes,
  getQuizById,
  createQuiz,
  startQuizAttempt,
  submitQuizAttempt,
  getQuizzesByCourse,
  getQuizzesByModule
};
