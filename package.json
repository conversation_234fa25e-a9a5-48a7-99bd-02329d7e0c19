{"name": "bumeran", "version": "1.0.0", "description": "Plataforma SaaS de aceleración de startups", "private": true, "workspaces": ["packages/*"], "scripts": {"start:backend": "cd packages/backend && npm run start", "start:frontend": "cd packages/frontend && npm run dev", "start:emails": "cd packages/emails && npm run start", "test:emails": "cd packages/emails && npm run test", "dev": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "dev:debug": "concurrently \"cd packages/backend && npm run debug:on\" \"npm run start:frontend\"", "build": "npm run build --workspaces", "test": "npm run test --workspaces", "lint": "npm run lint --workspaces", "debug:toggle": "cd packages/backend && npm run debug:toggle", "debug:on": "cd packages/backend && npm run debug:on", "debug:off": "cd packages/backend && npm run debug:off"}, "keywords": ["saas", "startup", "accelerator"], "author": "", "license": "ISC", "devDependencies": {"concurrently": "^8.2.2", "puppeteer": "^24.9.0"}, "dependencies": {"react-hook-form": "^7.56.4"}}