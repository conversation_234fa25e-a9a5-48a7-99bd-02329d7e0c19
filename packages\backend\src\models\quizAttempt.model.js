const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * Modelo QuizAttempt - Representa un intento de quiz por un usuario
 * 
 * Registra cada vez que un usuario intenta completar un quiz,
 * incluyendo respuestas, puntuación y tiempo empleado.
 */
const QuizAttempt = sequelize.define('QuizAttempt', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  quizId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Quizzes',
      key: 'id',
    },
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Users',
      key: 'id',
    },
  },
  attemptNumber: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 1,
    comment: 'Número de intento (1, 2, 3, etc.)',
  },
  startedAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
  completedAt: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  timeSpent: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Tiempo empleado en segundos',
  },
  score: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true,
    comment: 'Puntuación obtenida (porcentaje)',
  },
  totalPoints: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Puntos totales obtenidos',
  },
  maxPoints: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Puntos máximos posibles',
  },
  passed: {
    type: DataTypes.BOOLEAN,
    allowNull: true,
    comment: 'Si aprobó el quiz',
  },
  status: {
    type: DataTypes.ENUM('in_progress', 'completed', 'abandoned', 'expired'),
    allowNull: false,
    defaultValue: 'in_progress',
  },
  answers: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'JSON con las respuestas del usuario',
  },
  feedback: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Retroalimentación personalizada',
  },
  ipAddress: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'IP desde donde se realizó el intento',
  },
  userAgent: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'User agent del navegador',
  },
}, {
  timestamps: true,
  indexes: [
    {
      fields: ['quizId'],
    },
    {
      fields: ['userId'],
    },
    {
      fields: ['quizId', 'userId'],
    },
    {
      fields: ['status'],
    },
    {
      fields: ['passed'],
    },
    {
      fields: ['completedAt'],
    },
  ],
});

module.exports = QuizAttempt;
