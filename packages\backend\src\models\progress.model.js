const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * Modelo Progress - Representa el progreso detallado de un usuario
 * 
 * Registra el progreso específico en lecciones, módulos y actividades
 * para un seguimiento granular del aprendizaje.
 */
const Progress = sequelize.define('Progress', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Users',
      key: 'id',
    },
  },
  courseId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Courses',
      key: 'id',
    },
  },
  moduleId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'Modules',
      key: 'id',
    },
  },
  lessonId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'Lessons',
      key: 'id',
    },
  },
  type: {
    type: DataTypes.ENUM('lesson', 'module', 'quiz', 'course'),
    allowNull: false,
    comment: 'Tipo de elemento del que se registra progreso',
  },
  status: {
    type: DataTypes.ENUM('not_started', 'in_progress', 'completed', 'skipped'),
    allowNull: false,
    defaultValue: 'not_started',
  },
  startedAt: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  completedAt: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  lastAccessedAt: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  timeSpent: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: 'Tiempo empleado en segundos',
  },
  progressPercentage: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: false,
    defaultValue: 0.00,
    comment: 'Porcentaje de progreso (0-100)',
  },
  score: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true,
    comment: 'Puntuación obtenida (si aplica)',
  },
  attempts: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: 'Número de intentos realizados',
  },
  metadata: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'JSON con datos adicionales específicos del progreso',
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Notas del estudiante sobre este elemento',
  },
}, {
  timestamps: true,
  indexes: [
    {
      fields: ['userId'],
    },
    {
      fields: ['courseId'],
    },
    {
      fields: ['moduleId'],
    },
    {
      fields: ['lessonId'],
    },
    {
      fields: ['userId', 'courseId'],
    },
    {
      fields: ['userId', 'lessonId'],
      unique: true,
    },
    {
      fields: ['type'],
    },
    {
      fields: ['status'],
    },
    {
      fields: ['completedAt'],
    },
  ],
});

module.exports = Progress;
