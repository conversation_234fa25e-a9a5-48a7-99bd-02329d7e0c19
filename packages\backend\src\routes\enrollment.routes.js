const express = require('express');
const router = express.Router();
const { verifyToken } = require('../middlewares/auth.middleware');
const {
  enrollInCourse,
  getUserEnrollments,
  getCourseEnrollments,
  updateEnrollmentProgress,
  cancelEnrollment
} = require('../controllers/enrollment.controller');

/**
 * Rutas para gestión de inscripciones del LMS
 * Todas las rutas requieren autenticación
 */

// Inscribirse a un curso
// POST /api/enrollments/course/:courseId
router.post('/course/:courseId', verifyToken, enrollInCourse);

// Obtener inscripciones de un usuario
// GET /api/enrollments/user/:userId
router.get('/user/:userId?', verifyToken, getUserEnrollments);

// Obtener inscripciones de un curso
// GET /api/enrollments/course/:courseId/students
router.get('/course/:courseId/students', verifyToken, getCourseEnrollments);

// Actualizar progreso de inscripción
// PUT /api/enrollments/:enrollmentId/progress
router.put('/:enrollmentId/progress', verifyToken, updateEnrollmentProgress);

// Cancelar inscripción
// DELETE /api/enrollments/:enrollmentId
router.delete('/:enrollmentId', verifyToken, cancelEnrollment);

module.exports = router;
