const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * <PERSON><PERSON> QuizAnswer - Representa una opción de respuesta para una pregunta
 * 
 * Cada pregunta puede tener múltiples respuestas posibles,
 * donde una o más pueden ser correctas.
 */
const QuizAnswer = sequelize.define('QuizAnswer', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  questionId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'QuizQuestions',
      key: 'id',
    },
  },
  answerText: {
    type: DataTypes.TEXT,
    allowNull: false,
    validate: {
      notEmpty: true,
    },
  },
  isCorrect: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  },
  orderIndex: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: 'Orden de la respuesta dentro de la pregunta',
  },
  explanation: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Explicación de por qué esta respuesta es correcta/incorrecta',
  },
  imageUrl: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'URL de imagen asociada a la respuesta',
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
  },
}, {
  timestamps: true,
  indexes: [
    {
      fields: ['questionId'],
    },
    {
      fields: ['questionId', 'orderIndex'],
    },
    {
      fields: ['isCorrect'],
    },
    {
      fields: ['isActive'],
    },
  ],
});

module.exports = QuizAnswer;
