const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * Modelo Module - Representa un módulo dentro de un curso
 * 
 * Los módulos organizan el contenido del curso en secciones lógicas.
 * Cada módulo contiene múltiples lecciones.
 */
const Module = sequelize.define('Module', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  title: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [3, 200],
    },
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  courseId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Courses',
      key: 'id',
    },
  },
  orderIndex: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: 'Orden del módulo dentro del curso',
  },
  duration: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Duración estimada en minutos',
  },
  isPublished: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
  },
  prerequisites: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'JSON con IDs de módulos prerequisitos',
  },
  learningObjectives: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Objetivos de aprendizaje del módulo',
  },
}, {
  timestamps: true,
  indexes: [
    {
      fields: ['courseId'],
    },
    {
      fields: ['courseId', 'orderIndex'],
    },
    {
      fields: ['isPublished', 'isActive'],
    },
  ],
});

module.exports = Module;
