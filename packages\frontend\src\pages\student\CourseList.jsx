import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { FiBook, FiClock, FiUsers, FiStar, FiPlay, FiCheck } from 'react-icons/fi';
import { getCourses } from '../../services/course.service';
import { getUserEnrollments, enrollInCourse } from '../../services/enrollment.service';
import useAuth from '../../hooks/useAuth';

const StudentCourseList = () => {
  const [courses, setCourses] = useState([]);
  const [enrollments, setEnrollments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [enrolling, setEnrolling] = useState(null);
  const [error, setError] = useState('');
  const [filters, setFilters] = useState({
    category: '',
    difficulty: '',
    search: ''
  });

  const { user } = useAuth();

  useEffect(() => {
    loadData();
  }, [filters]);

  const loadData = async () => {
    try {
      setLoading(true);

      // Cargar cursos publicados
      const coursesResponse = await getCourses({
        isPublished: 'true',
        ...filters
      });

      // Cargar inscripciones del usuario
      const enrollmentsResponse = await getUserEnrollments();

      if (coursesResponse.success) {
        setCourses(coursesResponse.data);
      }

      if (enrollmentsResponse.success) {
        setEnrollments(enrollmentsResponse.data);
      }
    } catch (err) {
      setError('Error al cargar cursos: ' + (err.message || 'Error desconocido'));
    } finally {
      setLoading(false);
    }
  };

  const handleEnroll = async (courseId) => {
    try {
      setEnrolling(courseId);
      const response = await enrollInCourse(courseId);

      if (response.success) {
        // Recargar inscripciones
        const enrollmentsResponse = await getUserEnrollments();
        if (enrollmentsResponse.success) {
          setEnrollments(enrollmentsResponse.data);
        }
      }
    } catch (err) {
      setError('Error al inscribirse: ' + (err.message || 'Error desconocido'));
    } finally {
      setEnrolling(null);
    }
  };

  const isEnrolled = (courseId) => {
    return enrollments.some(enrollment => enrollment.courseId === courseId);
  };

  const getEnrollmentStatus = (courseId) => {
    const enrollment = enrollments.find(e => e.courseId === courseId);
    return enrollment ? enrollment.status : null;
  };

  const getProgressPercentage = (courseId) => {
    const enrollment = enrollments.find(e => e.courseId === courseId);
    return enrollment ? enrollment.progressPercentage : 0;
  };

  const getDifficultyBadge = (difficulty) => {
    const colors = {
      beginner: 'bg-green-100 text-green-800',
      intermediate: 'bg-yellow-100 text-yellow-800',
      advanced: 'bg-red-100 text-red-800'
    };
    const labels = {
      beginner: 'Principiante',
      intermediate: 'Intermedio',
      advanced: 'Avanzado'
    };
    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${colors[difficulty] || 'bg-gray-100 text-gray-800'}`}>
        {labels[difficulty] || difficulty}
      </span>
    );
  };

  const getStatusBadge = (status, progress) => {
    switch (status) {
      case 'completed':
        return (
          <span className="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 flex items-center gap-1">
            <FiCheck className="w-3 h-3" />
            Completado
          </span>
        );
      case 'in_progress':
        return (
          <span className="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 flex items-center gap-1">
            <FiPlay className="w-3 h-3" />
            En progreso ({progress.toFixed(0)}%)
          </span>
        );
      case 'enrolled':
        return (
          <span className="px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            Inscrito
          </span>
        );
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Catálogo de Cursos</h1>
        <p className="text-gray-600">Descubre y aprende con nuestros cursos especializados</p>
      </div>

      {/* Filtros */}
      <div className="bg-white p-4 rounded-lg shadow-sm border">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Buscar
            </label>
            <input
              type="text"
              value={filters.search}
              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Buscar cursos..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Categoría
            </label>
            <select
              value={filters.category}
              onChange={(e) => setFilters(prev => ({ ...prev, category: e.target.value }))}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Todas las categorías</option>
              <option value="Emprendimiento">Emprendimiento</option>
              <option value="Marketing">Marketing</option>
              <option value="Finanzas">Finanzas</option>
              <option value="Tecnología">Tecnología</option>
              <option value="Liderazgo">Liderazgo</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Dificultad
            </label>
            <select
              value={filters.difficulty}
              onChange={(e) => setFilters(prev => ({ ...prev, difficulty: e.target.value }))}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Todas las dificultades</option>
              <option value="beginner">Principiante</option>
              <option value="intermediate">Intermedio</option>
              <option value="advanced">Avanzado</option>
            </select>
          </div>
        </div>
      </div>

      {/* Error */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          {error}
        </div>
      )}

      {/* Lista de Cursos */}
      {courses.length === 0 ? (
        <div className="text-center py-12">
          <FiBook className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No hay cursos disponibles</h3>
          <p className="text-gray-600">No se encontraron cursos que coincidan con tus filtros</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {courses.map((course) => {
            const enrolled = isEnrolled(course.id);
            const status = getEnrollmentStatus(course.id);
            const progress = getProgressPercentage(course.id);

            return (
              <div key={course.id} className="bg-white rounded-lg shadow-sm border overflow-hidden hover:shadow-md transition-shadow">
                {/* Imagen */}
                <div className="h-48 bg-gray-200 relative">
                  {course.thumbnail ? (
                    <img
                      src={course.thumbnail}
                      alt={course.title}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <FiBook className="w-12 h-12 text-gray-400" />
                    </div>
                  )}

                  {/* Badge de estado */}
                  {enrolled && (
                    <div className="absolute top-2 right-2">
                      {getStatusBadge(status, progress)}
                    </div>
                  )}
                </div>

                {/* Contenido */}
                <div className="p-4">
                  <div className="flex items-start justify-between mb-2">
                    <h3 className="text-lg font-semibold text-gray-900 line-clamp-2">
                      {course.title}
                    </h3>
                  </div>

                  <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                    {course.shortDescription || course.description}
                  </p>

                  {/* Metadatos */}
                  <div className="flex items-center gap-4 text-sm text-gray-500 mb-3">
                    <div className="flex items-center gap-1">
                      <FiClock className="w-4 h-4" />
                      <span>{course.duration || 0}min</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <FiUsers className="w-4 h-4" />
                      <span>{course.totalEnrollments || 0}</span>
                    </div>
                  </div>

                  {/* Badges */}
                  <div className="flex items-center gap-2 mb-4">
                    {getDifficultyBadge(course.difficulty)}
                    {course.category && (
                      <span className="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {course.category}
                      </span>
                    )}
                  </div>

                  {/* Barra de progreso para cursos inscritos */}
                  {enrolled && status === 'in_progress' && (
                    <div className="mb-4">
                      <div className="flex justify-between text-sm text-gray-600 mb-1">
                        <span>Progreso</span>
                        <span>{progress.toFixed(0)}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${progress}%` }}
                        ></div>
                      </div>
                    </div>
                  )}

                  {/* Acciones */}
                  <div className="flex gap-2">
                    {enrolled ? (
                      <Link
                        to={`/student/courses/${course.id}`}
                        className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-center text-sm font-medium transition-colors"
                      >
                        {status === 'completed' ? 'Revisar' : 'Continuar'}
                      </Link>
                    ) : (
                      <>
                        <Link
                          to={`/student/courses/${course.id}/preview`}
                          className="flex-1 border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-center text-sm font-medium hover:bg-gray-50 transition-colors"
                        >
                          Ver Detalles
                        </Link>
                        <button
                          onClick={() => handleEnroll(course.id)}
                          disabled={enrolling === course.id}
                          className="flex-1 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors disabled:opacity-50 flex items-center justify-center gap-1"
                        >
                          {enrolling === course.id ? (
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                          ) : (
                            <>
                              <FiPlay className="w-4 h-4" />
                              Inscribirse
                            </>
                          )}
                        </button>
                      </>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default StudentCourseList;
