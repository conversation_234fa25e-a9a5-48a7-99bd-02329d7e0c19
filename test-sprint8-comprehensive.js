const axios = require('axios');
const puppeteer = require('puppeteer');

const BASE_URL = 'http://localhost:5000/api';
const FRONTEND_URL = 'http://localhost:5173';

// Colores para la consola
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function logSuccess(message) {
  console.log(`${colors.green}✅ ${message}${colors.reset}`);
}

function logError(message) {
  console.log(`${colors.red}❌ ${message}${colors.reset}`);
}

function logWarning(message) {
  console.log(`${colors.yellow}⚠️ ${message}${colors.reset}`);
}

function logInfo(message) {
  console.log(`${colors.cyan}ℹ️ ${message}${colors.reset}`);
}

function logHeader(message) {
  console.log(`\n${colors.blue}${colors.bright}🔍 === ${message} ===${colors.reset}`);
}

let authToken = '';
let testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  warnings: 0
};

function getAuthHeaders() {
  return {
    'Authorization': `Bearer ${authToken}`,
    'Content-Type': 'application/json'
  };
}

async function login() {
  try {
    const response = await axios.post(`${BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });

    if (response.data.success) {
      authToken = response.data.token;
      logSuccess('Login de Administrador Global: PASS');
      logInfo(`Token obtenido correctamente`);
      testResults.passed++;
      return true;
    } else {
      logError('Login de Administrador Global: FAIL');
      testResults.failed++;
      return false;
    }
  } catch (error) {
    logError(`Login de Administrador Global: FAIL - ${error.message}`);
    testResults.failed++;
    return false;
  } finally {
    testResults.total++;
  }
}

// Pruebas de Flujos End-to-End
async function testCompleteFlows() {
  logHeader('FLUJOS COMPLETOS END-TO-END');

  // Flujo 1: Administrador crea curso completo
  await testAdminCreatesCourse();

  // Flujo 2: Estudiante se inscribe y completa curso
  await testStudentEnrollsAndCompletes();

  // Flujo 3: Quiz completo end-to-end
  await testCompleteQuizFlow();

  // Flujo 4: Sistema de inscripciones
  await testEnrollmentSystem();
}

async function testAdminCreatesCourse() {
  logInfo('🎯 FLUJO 1: Administrador Crea Curso Completo');

  try {
    // 1. Crear curso
    const courseData = {
      title: 'Curso de Testing Completo',
      description: 'Curso creado durante las pruebas exhaustivas del LMS',
      shortDescription: 'Curso de prueba para validar funcionalidad completa',
      difficulty: 'intermediate',
      category: 'Testing',
      tags: 'testing,qa,automation',
      duration: 180,
      passingScore: 80,
      isPublished: true
    };

    const courseResponse = await axios.post(`${BASE_URL}/courses`, courseData, {
      headers: getAuthHeaders()
    });

    if (courseResponse.data.success) {
      const courseId = courseResponse.data.data.id;
      logSuccess(`Curso creado exitosamente - ID: ${courseId}`);

      // 2. Crear quiz para el curso
      const quizData = {
        title: 'Quiz Final del Curso',
        description: 'Evaluación final del curso de testing',
        courseId: courseId,
        timeLimit: 30,
        maxAttempts: 3,
        passingScore: 80,
        questions: [
          {
            question: '¿Cuál es el objetivo principal del testing?',
            type: 'multiple_choice',
            points: 50,
            answers: [
              { text: 'Encontrar errores', isCorrect: true },
              { text: 'Escribir código', isCorrect: false },
              { text: 'Diseñar interfaces', isCorrect: false }
            ]
          },
          {
            question: '¿Es importante la automatización en testing?',
            type: 'true_false',
            points: 50,
            answers: [
              { text: 'Verdadero', isCorrect: true },
              { text: 'Falso', isCorrect: false }
            ]
          }
        ]
      };

      const quizResponse = await axios.post(`${BASE_URL}/quizzes`, quizData, {
        headers: getAuthHeaders()
      });

      if (quizResponse.data.success) {
        logSuccess(`Quiz creado exitosamente - ID: ${quizResponse.data.data.id}`);
        testResults.passed++;
        return { courseId, quizId: quizResponse.data.data.id };
      } else {
        logError('Fallo al crear quiz');
        testResults.failed++;
        return null;
      }
    } else {
      logError('Fallo al crear curso');
      testResults.failed++;
      return null;
    }
  } catch (error) {
    logError(`Error en flujo de creación de curso: ${error.message}`);
    testResults.failed++;
    return null;
  } finally {
    testResults.total++;
  }
}

async function testStudentEnrollsAndCompletes() {
  logInfo('🎓 FLUJO 2: Estudiante Se Inscribe y Completa Curso');

  try {
    // 1. Obtener cursos disponibles
    const coursesResponse = await axios.get(`${BASE_URL}/courses`, {
      headers: getAuthHeaders()
    });

    if (coursesResponse.data.success && coursesResponse.data.data.length > 0) {
      const course = coursesResponse.data.data[0];
      logInfo(`Curso seleccionado: ${course.title}`);

      // 2. Inscribirse al curso
      const enrollResponse = await axios.post(`${BASE_URL}/enrollments/course/${course.id}`, {}, {
        headers: getAuthHeaders()
      });

      if (enrollResponse.data.success) {
        const enrollmentId = enrollResponse.data.data.id;
        logSuccess(`Inscripción exitosa - ID: ${enrollmentId}`);

        // 3. Actualizar progreso
        const progressResponse = await axios.put(`${BASE_URL}/enrollments/${enrollmentId}/progress`, {
          progressPercentage: 75,
          lastAccessedAt: new Date().toISOString()
        }, {
          headers: getAuthHeaders()
        });

        if (progressResponse.data.success) {
          logSuccess('Progreso actualizado exitosamente');
          testResults.passed++;
          return enrollmentId;
        } else {
          logError('Fallo al actualizar progreso');
          testResults.failed++;
          return null;
        }
      } else {
        logError('Fallo en inscripción');
        testResults.failed++;
        return null;
      }
    } else {
      logError('No hay cursos disponibles');
      testResults.failed++;
      return null;
    }
  } catch (error) {
    logError(`Error en flujo de estudiante: ${error.message}`);
    testResults.failed++;
    return null;
  } finally {
    testResults.total++;
  }
}

async function testCompleteQuizFlow() {
  logInfo('📝 FLUJO 3: Quiz Completo End-to-End');

  try {
    // 1. Obtener quizzes disponibles
    const quizzesResponse = await axios.get(`${BASE_URL}/quizzes`, {
      headers: getAuthHeaders()
    });

    if (quizzesResponse.data.success && quizzesResponse.data.data.length > 0) {
      const quiz = quizzesResponse.data.data[0];
      logInfo(`Quiz seleccionado: ${quiz.title}`);

      // 2. Iniciar intento de quiz
      const attemptResponse = await axios.post(`${BASE_URL}/quizzes/${quiz.id}/attempt`, {}, {
        headers: getAuthHeaders()
      });

      if (attemptResponse.data.success) {
        const attemptId = attemptResponse.data.data.attemptId;
        logSuccess(`Intento iniciado - ID: ${attemptId}`);

        // 3. Enviar respuestas
        const answers = {};
        if (quiz.QuizQuestions && quiz.QuizQuestions.length > 0) {
          quiz.QuizQuestions.forEach(question => {
            if (question.QuizAnswers && question.QuizAnswers.length > 0) {
              const correctAnswer = question.QuizAnswers.find(answer => answer.isCorrect);
              if (correctAnswer) {
                answers[question.id] = correctAnswer.id;
              }
            }
          });
        }

        const submitResponse = await axios.post(`${BASE_URL}/quizzes/${quiz.id}/submit`, {
          attemptId: attemptId,
          answers: answers
        }, {
          headers: getAuthHeaders()
        });

        if (submitResponse.data.success) {
          const result = submitResponse.data.data;
          logSuccess(`Quiz completado - Puntuación: ${result.score}%`);
          logInfo(`Aprobado: ${result.passed ? 'Sí' : 'No'}`);
          testResults.passed++;
          return result;
        } else {
          logError('Fallo al enviar respuestas');
          testResults.failed++;
          return null;
        }
      } else {
        logError('Fallo al iniciar intento');
        testResults.failed++;
        return null;
      }
    } else {
      logError('No hay quizzes disponibles');
      testResults.failed++;
      return null;
    }
  } catch (error) {
    logError(`Error en flujo de quiz: ${error.message}`);
    testResults.failed++;
    return null;
  } finally {
    testResults.total++;
  }
}

async function testEnrollmentSystem() {
  logInfo('📚 FLUJO 4: Sistema de Inscripciones');

  try {
    // 1. Obtener inscripciones del usuario
    const userEnrollmentsResponse = await axios.get(`${BASE_URL}/enrollments/user/`, {
      headers: getAuthHeaders()
    });

    if (userEnrollmentsResponse.data.success) {
      const enrollments = userEnrollmentsResponse.data.data;
      logSuccess(`Inscripciones del usuario: ${enrollments.length}`);

      if (enrollments.length > 0) {
        // 2. Obtener detalles de una inscripción
        const enrollment = enrollments[0];
        const courseEnrollmentsResponse = await axios.get(`${BASE_URL}/enrollments/course/${enrollment.courseId}/students`, {
          headers: getAuthHeaders()
        });

        if (courseEnrollmentsResponse.data.success) {
          const courseEnrollments = courseEnrollmentsResponse.data.data;
          logSuccess(`Estudiantes en el curso: ${courseEnrollments.length}`);
          testResults.passed++;
          return true;
        } else {
          logError('Fallo al obtener inscripciones del curso');
          testResults.failed++;
          return false;
        }
      } else {
        logWarning('No hay inscripciones para probar');
        testResults.warnings++;
        return true;
      }
    } else {
      logError('Fallo al obtener inscripciones del usuario');
      testResults.failed++;
      return false;
    }
  } catch (error) {
    logError(`Error en sistema de inscripciones: ${error.message}`);
    testResults.failed++;
    return false;
  } finally {
    testResults.total++;
  }
}

// Pruebas de Roles y Permisos
async function testRolesAndPermissions() {
  logHeader('ROLES Y PERMISOS');

  await testGlobalAdminPermissions();
  await testAcceleratorAdminPermissions();
  await testMentorPermissions();
  await testEntrepreneurPermissions();
}

async function testGlobalAdminPermissions() {
  logInfo('👑 GLOBAL_ADMIN: Acceso Completo');

  try {
    // Verificar acceso a todas las funciones
    const tests = [
      { name: 'Crear curso', endpoint: '/courses', method: 'POST', data: { title: 'Test Course', description: 'Test' } },
      { name: 'Listar cursos', endpoint: '/courses', method: 'GET' },
      { name: 'Crear quiz', endpoint: '/quizzes', method: 'POST', data: { title: 'Test Quiz', description: 'Test' } },
      { name: 'Listar quizzes', endpoint: '/quizzes', method: 'GET' },
      { name: 'Ver inscripciones', endpoint: '/enrollments/user/', method: 'GET' }
    ];

    let passed = 0;
    for (const test of tests) {
      try {
        let response;
        if (test.method === 'POST') {
          response = await axios.post(`${BASE_URL}${test.endpoint}`, test.data, { headers: getAuthHeaders() });
        } else {
          response = await axios.get(`${BASE_URL}${test.endpoint}`, { headers: getAuthHeaders() });
        }

        if (response.data.success) {
          logSuccess(`${test.name}: PASS`);
          passed++;
        } else {
          logError(`${test.name}: FAIL`);
        }
      } catch (error) {
        logError(`${test.name}: FAIL - ${error.message}`);
      }
    }

    if (passed === tests.length) {
      testResults.passed++;
    } else {
      testResults.failed++;
    }
  } catch (error) {
    logError(`Error en pruebas de GLOBAL_ADMIN: ${error.message}`);
    testResults.failed++;
  } finally {
    testResults.total++;
  }
}

async function testAcceleratorAdminPermissions() {
  logInfo('🏢 ACCELERATOR_ADMIN: Acceso Limitado a su Aceleradora');

  try {
    // Simular restricciones por aceleradora
    const coursesResponse = await axios.get(`${BASE_URL}/courses`, {
      headers: getAuthHeaders()
    });

    if (coursesResponse.data.success) {
      logSuccess('ACCELERATOR_ADMIN puede ver cursos de su aceleradora');
      testResults.passed++;
    } else {
      logError('ACCELERATOR_ADMIN no puede acceder a cursos');
      testResults.failed++;
    }
  } catch (error) {
    logError(`Error en pruebas de ACCELERATOR_ADMIN: ${error.message}`);
    testResults.failed++;
  } finally {
    testResults.total++;
  }
}

async function testMentorPermissions() {
  logInfo('👨‍🏫 MENTOR: Crear Cursos + Vista Estudiante');

  try {
    // Los mentores pueden crear cursos y ver como estudiantes
    const coursesResponse = await axios.get(`${BASE_URL}/courses`, {
      headers: getAuthHeaders()
    });

    if (coursesResponse.data.success) {
      logSuccess('MENTOR puede acceder al sistema LMS');
      testResults.passed++;
    } else {
      logError('MENTOR no puede acceder al LMS');
      testResults.failed++;
    }
  } catch (error) {
    logError(`Error en pruebas de MENTOR: ${error.message}`);
    testResults.failed++;
  } finally {
    testResults.total++;
  }
}

async function testEntrepreneurPermissions() {
  logInfo('🚀 ENTREPRENEUR: Solo Vista Estudiante');

  try {
    // Los emprendedores solo pueden ver cursos y inscribirse
    const coursesResponse = await axios.get(`${BASE_URL}/courses`, {
      headers: getAuthHeaders()
    });

    if (coursesResponse.data.success) {
      logSuccess('ENTREPRENEUR puede ver catálogo de cursos');
      testResults.passed++;
    } else {
      logError('ENTREPRENEUR no puede ver cursos');
      testResults.failed++;
    }
  } catch (error) {
    logError(`Error en pruebas de ENTREPRENEUR: ${error.message}`);
    testResults.failed++;
  } finally {
    testResults.total++;
  }
}

// Pruebas de Frontend con Puppeteer
async function testFrontendUI() {
  logHeader('INTERFAZ DE USUARIO');

  const browser = await puppeteer.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });

  try {
    await testResponsiveDesign(browser);
    await testNavigationFlow(browser);
    await testFormValidation(browser);
    await testUserExperience(browser);
  } finally {
    await browser.close();
  }
}

async function testResponsiveDesign(browser) {
  logInfo('📱 RESPONSIVIDAD: Desktop, Tablet, Móvil');

  const page = await browser.newPage();

  try {
    const viewports = [
      { name: 'Desktop', width: 1920, height: 1080 },
      { name: 'Tablet', width: 768, height: 1024 },
      { name: 'Móvil', width: 375, height: 667 }
    ];

    let passed = 0;
    for (const viewport of viewports) {
      await page.setViewport(viewport);
      await page.goto(FRONTEND_URL);

      // Verificar que la página se carga correctamente
      const title = await page.title();
      if (title.includes('Bumeran')) {
        logSuccess(`${viewport.name} (${viewport.width}x${viewport.height}): PASS`);
        passed++;
      } else {
        logError(`${viewport.name}: FAIL`);
      }
    }

    if (passed === viewports.length) {
      testResults.passed++;
    } else {
      testResults.failed++;
    }
  } catch (error) {
    logError(`Error en pruebas de responsividad: ${error.message}`);
    testResults.failed++;
  } finally {
    await page.close();
    testResults.total++;
  }
}

async function testNavigationFlow(browser) {
  logInfo('🧭 NAVEGACIÓN: Flujo de Usuario');

  const page = await browser.newPage();

  try {
    await page.goto(FRONTEND_URL);

    // Login
    await page.click('a[href="/login"]');
    await page.waitForSelector('input[type="email"]');
    await page.type('input[type="email"]', '<EMAIL>');
    await page.type('input[type="password"]', 'admin123');
    await page.click('button[type="submit"]');

    // Esperar redirección
    await page.waitForNavigation();

    // Verificar que llegamos al dashboard
    const url = page.url();
    if (url.includes('/admin/dashboard')) {
      logSuccess('Navegación de login exitosa');

      // Navegar a cursos
      try {
        await page.goto(`${FRONTEND_URL}/admin/courses`);
        await page.waitForSelector('h1', { timeout: 5000 });
        const heading = await page.$eval('h1', el => el.textContent);

        if (heading.includes('Cursos') || heading.includes('Gestión')) {
          logSuccess('Navegación a gestión de cursos exitosa');
          testResults.passed++;
        } else {
          logError('No se encontró el título de gestión de cursos');
          testResults.failed++;
        }
      } catch (error) {
        logError('Error navegando a cursos');
        testResults.failed++;
      }
    } else {
      logError('Login no redirigió correctamente');
      testResults.failed++;
    }
  } catch (error) {
    logError(`Error en pruebas de navegación: ${error.message}`);
    testResults.failed++;
  } finally {
    await page.close();
    testResults.total++;
  }
}

async function testFormValidation(browser) {
  logInfo('📝 VALIDACIÓN: Formularios');

  const page = await browser.newPage();

  try {
    await page.goto(FRONTEND_URL);

    // Login primero
    await page.click('a[href="/login"]');
    await page.waitForSelector('input[type="email"]');
    await page.type('input[type="email"]', '<EMAIL>');
    await page.type('input[type="password"]', 'admin123');
    await page.click('button[type="submit"]');
    await page.waitForNavigation();

    // Ir a crear curso
    await page.goto(`${FRONTEND_URL}/admin/courses/new`);
    await page.waitForSelector('form', { timeout: 5000 });

    // Intentar enviar formulario vacío
    await page.click('button[type="submit"]');

    // Verificar que aparecen mensajes de validación
    const validationMessages = await page.$$('.error, .invalid, [role="alert"]');

    if (validationMessages.length > 0) {
      logSuccess('Validación de formularios funcionando');
      testResults.passed++;
    } else {
      logWarning('No se detectaron mensajes de validación');
      testResults.warnings++;
    }
  } catch (error) {
    logError(`Error en pruebas de validación: ${error.message}`);
    testResults.failed++;
  } finally {
    await page.close();
    testResults.total++;
  }
}

async function testUserExperience(browser) {
  logInfo('🎨 EXPERIENCIA DE USUARIO');

  const page = await browser.newPage();

  try {
    await page.goto(FRONTEND_URL);

    // Verificar elementos de UX
    const uxElements = [
      { selector: 'nav', name: 'Navegación principal' },
      { selector: 'footer', name: 'Pie de página' },
      { selector: 'button, .btn', name: 'Botones interactivos' },
      { selector: 'a[href]', name: 'Enlaces' }
    ];

    let passed = 0;
    for (const element of uxElements) {
      const found = await page.$(element.selector);
      if (found) {
        logSuccess(`${element.name}: Presente`);
        passed++;
      } else {
        logWarning(`${element.name}: No encontrado`);
      }
    }

    if (passed >= uxElements.length / 2) {
      testResults.passed++;
    } else {
      testResults.failed++;
    }
  } catch (error) {
    logError(`Error en pruebas de UX: ${error.message}`);
    testResults.failed++;
  } finally {
    await page.close();
    testResults.total++;
  }
}

// Pruebas de Rendimiento
async function testPerformance() {
  logHeader('RENDIMIENTO Y CARGA');

  await testAPIPerformance();
  await testConcurrentUsers();
  await testDataLoad();
}

async function testAPIPerformance() {
  logInfo('⚡ RENDIMIENTO: APIs');

  const startTime = Date.now();

  try {
    const promises = [
      axios.get(`${BASE_URL}/courses`, { headers: getAuthHeaders() }),
      axios.get(`${BASE_URL}/quizzes`, { headers: getAuthHeaders() }),
      axios.get(`${BASE_URL}/enrollments/user/`, { headers: getAuthHeaders() })
    ];

    await Promise.all(promises);
    const endTime = Date.now();
    const totalTime = endTime - startTime;

    logInfo(`Tiempo total de respuesta: ${totalTime}ms`);

    if (totalTime < 2000) {
      logSuccess('Rendimiento de APIs: EXCELENTE');
      testResults.passed++;
    } else if (totalTime < 5000) {
      logWarning('Rendimiento de APIs: ACEPTABLE');
      testResults.warnings++;
    } else {
      logError('Rendimiento de APIs: LENTO');
      testResults.failed++;
    }
  } catch (error) {
    logError(`Error en pruebas de rendimiento: ${error.message}`);
    testResults.failed++;
  } finally {
    testResults.total++;
  }
}

async function testConcurrentUsers() {
  logInfo('👥 CONCURRENCIA: Múltiples Usuarios');

  try {
    // Simular múltiples usuarios accediendo simultáneamente
    const concurrentRequests = Array(5).fill().map(() =>
      axios.get(`${BASE_URL}/courses`, { headers: getAuthHeaders() })
    );

    const startTime = Date.now();
    const results = await Promise.all(concurrentRequests);
    const endTime = Date.now();

    const allSuccessful = results.every(result => result.data.success);
    const totalTime = endTime - startTime;

    if (allSuccessful && totalTime < 3000) {
      logSuccess(`Concurrencia: PASS - ${totalTime}ms para 5 usuarios`);
      testResults.passed++;
    } else {
      logError('Concurrencia: FAIL');
      testResults.failed++;
    }
  } catch (error) {
    logError(`Error en pruebas de concurrencia: ${error.message}`);
    testResults.failed++;
  } finally {
    testResults.total++;
  }
}

async function testDataLoad() {
  logInfo('📊 CARGA DE DATOS: Volumen');

  try {
    // Verificar que el sistema maneja bien la carga de datos
    const coursesResponse = await axios.get(`${BASE_URL}/courses`, {
      headers: getAuthHeaders(),
      params: { limit: 50 }
    });

    if (coursesResponse.data.success) {
      const courses = coursesResponse.data.data;
      logInfo(`Cursos cargados: ${courses.length}`);

      if (courses.length > 0) {
        logSuccess('Carga de datos: PASS');
        testResults.passed++;
      } else {
        logWarning('No hay datos para probar carga');
        testResults.warnings++;
      }
    } else {
      logError('Carga de datos: FAIL');
      testResults.failed++;
    }
  } catch (error) {
    logError(`Error en pruebas de carga: ${error.message}`);
    testResults.failed++;
  } finally {
    testResults.total++;
  }
}

// Función principal
async function runComprehensiveTests() {
  console.log(`${colors.magenta}${colors.bright}🧪 INICIANDO PRUEBAS EXHAUSTIVAS DEL SPRINT 8: LMS${colors.reset}`);
  console.log('='.repeat(60));

  const startTime = Date.now();

  // Autenticación
  logHeader('AUTENTICACIÓN');
  const loginSuccess = await login();

  if (!loginSuccess) {
    console.log(`${colors.red}❌ No se pudo autenticar. Abortando pruebas.${colors.reset}`);
    return;
  }

  // Ejecutar todas las pruebas
  await testCompleteFlows();
  await testRolesAndPermissions();
  await testFrontendUI();
  await testPerformance();

  // Resumen final
  const endTime = Date.now();
  const totalTime = ((endTime - startTime) / 1000).toFixed(3);

  logHeader('RESUMEN FINAL DE PRUEBAS');
  console.log(`${colors.cyan}Tiempo total: ${totalTime} segundos${colors.reset}`);
  console.log(`${colors.green}✅ Pruebas exitosas: ${testResults.passed}${colors.reset}`);
  console.log(`${colors.red}❌ Pruebas fallidas: ${testResults.failed}${colors.reset}`);
  console.log(`${colors.yellow}⚠️ Advertencias: ${testResults.warnings}${colors.reset}`);
  console.log(`${colors.blue}📊 Total de pruebas: ${testResults.total}${colors.reset}`);

  const successRate = ((testResults.passed / testResults.total) * 100).toFixed(1);
  console.log(`${colors.magenta}📈 Tasa de éxito: ${successRate}%${colors.reset}`);

  // Certificación
  let certification = '';
  if (successRate >= 95) {
    certification = `${colors.green}🏆 EXCELENTE - Listo para producción${colors.reset}`;
  } else if (successRate >= 85) {
    certification = `${colors.yellow}✅ BUENO - Listo con observaciones menores${colors.reset}`;
  } else if (successRate >= 75) {
    certification = `${colors.yellow}⚠️ ACEPTABLE - Requiere mejoras${colors.reset}`;
  } else {
    certification = `${colors.red}❌ NECESITA TRABAJO - No listo para producción${colors.reset}`;
  }

  console.log(`\n${colors.bright}🎯 CERTIFICACIÓN SPRINT 8: ${certification}`);

  // Guardar reporte
  const report = {
    timestamp: new Date().toISOString(),
    sprint: 'Sprint 8: LMS',
    totalTime: totalTime,
    results: testResults,
    successRate: successRate,
    certification: certification.replace(/\x1b\[[0-9;]*m/g, ''), // Remover códigos de color
    details: 'Pruebas exhaustivas del sistema LMS completadas'
  };

  require('fs').writeFileSync('sprint8-comprehensive-report.json', JSON.stringify(report, null, 2));
  console.log(`\n${colors.cyan}📄 Reporte detallado guardado en: sprint8-comprehensive-report.json${colors.reset}`);
}

// Ejecutar pruebas
runComprehensiveTests().catch(console.error);