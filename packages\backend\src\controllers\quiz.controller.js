const { 
  Quiz, 
  QuizQuestion, 
  QuizAnswer, 
  QuizAttempt, 
  Course, 
  Module, 
  User 
} = require('../models');
const { debugLog } = require('../utils/debug');

/**
 * Controlador para gestión de quizzes del LMS
 */

// Obtener todos los quizzes
const getQuizzes = async (req, res) => {
  try {
    debugLog('Obteniendo lista de quizzes');
    
    const { 
      page = 1, 
      limit = 10, 
      courseId, 
      moduleId 
    } = req.query;

    const offset = (page - 1) * limit;
    const where = {};

    if (courseId) where.courseId = courseId;
    if (moduleId) where.moduleId = moduleId;

    const quizzes = await Quiz.findAndCountAll({
      where,
      include: [
        {
          model: Course,
          attributes: ['id', 'title']
        },
        {
          model: Module,
          attributes: ['id', 'title']
        },
        {
          model: User,
          as: 'Creator',
          attributes: ['id', 'firstName', 'lastName']
        },
        {
          model: QuizQuestion,
          as: 'Questions',
          attributes: ['id', 'question', 'type', 'points']
        }
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['createdAt', 'DESC']]
    });

    res.json({
      success: true,
      data: quizzes.rows,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: quizzes.count,
        pages: Math.ceil(quizzes.count / limit)
      }
    });

  } catch (error) {
    debugLog('Error al obtener quizzes:', error);
    res.status(500).json({
      success: false,
      message: 'Error al obtener quizzes',
      error: error.message
    });
  }
};

// Obtener un quiz por ID con preguntas y respuestas
const getQuizById = async (req, res) => {
  try {
    const { id } = req.params;
    debugLog(`Obteniendo quiz con ID: ${id}`);

    const quiz = await Quiz.findByPk(id, {
      include: [
        {
          model: Course,
          attributes: ['id', 'title']
        },
        {
          model: Module,
          attributes: ['id', 'title']
        },
        {
          model: User,
          as: 'Creator',
          attributes: ['id', 'firstName', 'lastName']
        },
        {
          model: QuizQuestion,
          as: 'Questions',
          include: [
            {
              model: QuizAnswer,
              as: 'Answers',
              attributes: ['id', 'answerText', 'isCorrect', 'orderIndex']
            }
          ],
          order: [['orderIndex', 'ASC']]
        }
      ]
    });

    if (!quiz) {
      return res.status(404).json({
        success: false,
        message: 'Quiz no encontrado'
      });
    }

    res.json({
      success: true,
      data: quiz
    });

  } catch (error) {
    debugLog('Error al obtener quiz:', error);
    res.status(500).json({
      success: false,
      message: 'Error al obtener quiz',
      error: error.message
    });
  }
};

// Crear un nuevo quiz
const createQuiz = async (req, res) => {
  try {
    debugLog('Creando nuevo quiz');

    const {
      title,
      description,
      instructions,
      courseId,
      moduleId,
      timeLimit,
      maxAttempts,
      passingScore,
      shuffleQuestions,
      shuffleAnswers,
      showCorrectAnswers,
      showScoreImmediately,
      questions
    } = req.body;

    // Validaciones básicas
    if (!title || !description) {
      return res.status(400).json({
        success: false,
        message: 'Título y descripción son requeridos'
      });
    }

    // Crear el quiz
    const quiz = await Quiz.create({
      title,
      description,
      instructions,
      courseId,
      moduleId,
      timeLimit,
      maxAttempts: maxAttempts || 3,
      passingScore: passingScore || 70,
      shuffleQuestions: shuffleQuestions || false,
      shuffleAnswers: shuffleAnswers || false,
      showCorrectAnswers: showCorrectAnswers !== false,
      showScoreImmediately: showScoreImmediately !== false,
      createdById: req.user.id
    });

    // Si se proporcionaron preguntas, crearlas
    if (questions && Array.isArray(questions)) {
      for (let i = 0; i < questions.length; i++) {
        const questionData = questions[i];
        
        const question = await QuizQuestion.create({
          quizId: quiz.id,
          question: questionData.question,
          type: questionData.type || 'multiple_choice',
          orderIndex: i,
          points: questionData.points || 1,
          explanation: questionData.explanation
        });

        // Crear respuestas si se proporcionaron
        if (questionData.answers && Array.isArray(questionData.answers)) {
          for (let j = 0; j < questionData.answers.length; j++) {
            const answerData = questionData.answers[j];
            
            await QuizAnswer.create({
              questionId: question.id,
              answerText: answerData.answerText,
              isCorrect: answerData.isCorrect || false,
              orderIndex: j,
              explanation: answerData.explanation
            });
          }
        }
      }
    }

    // Obtener el quiz creado con todas las relaciones
    const createdQuiz = await Quiz.findByPk(quiz.id, {
      include: [
        {
          model: QuizQuestion,
          as: 'Questions',
          include: [
            {
              model: QuizAnswer,
              as: 'Answers'
            }
          ]
        }
      ]
    });

    res.status(201).json({
      success: true,
      message: 'Quiz creado exitosamente',
      data: createdQuiz
    });

  } catch (error) {
    debugLog('Error al crear quiz:', error);
    res.status(500).json({
      success: false,
      message: 'Error al crear quiz',
      error: error.message
    });
  }
};

// Iniciar un intento de quiz
const startQuizAttempt = async (req, res) => {
  try {
    const { id } = req.params;
    debugLog(`Iniciando intento de quiz con ID: ${id}`);

    const quiz = await Quiz.findByPk(id);
    if (!quiz) {
      return res.status(404).json({
        success: false,
        message: 'Quiz no encontrado'
      });
    }

    // Verificar intentos previos
    const previousAttempts = await QuizAttempt.count({
      where: {
        quizId: id,
        userId: req.user.id
      }
    });

    if (previousAttempts >= quiz.maxAttempts) {
      return res.status(400).json({
        success: false,
        message: 'Has alcanzado el número máximo de intentos para este quiz'
      });
    }

    // Crear nuevo intento
    const attempt = await QuizAttempt.create({
      quizId: id,
      userId: req.user.id,
      attemptNumber: previousAttempts + 1,
      startedAt: new Date(),
      status: 'in_progress',
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    // Obtener preguntas del quiz (sin respuestas correctas para el estudiante)
    const quizWithQuestions = await Quiz.findByPk(id, {
      include: [
        {
          model: QuizQuestion,
          as: 'Questions',
          include: [
            {
              model: QuizAnswer,
              as: 'Answers',
              attributes: ['id', 'answerText', 'orderIndex'] // Sin isCorrect
            }
          ],
          order: quiz.shuffleQuestions ? 
            [['orderIndex', 'ASC']] : // TODO: Implementar shuffle real
            [['orderIndex', 'ASC']]
        }
      ]
    });

    res.json({
      success: true,
      message: 'Intento de quiz iniciado',
      data: {
        attempt: {
          id: attempt.id,
          attemptNumber: attempt.attemptNumber,
          startedAt: attempt.startedAt,
          timeLimit: quiz.timeLimit
        },
        quiz: quizWithQuestions
      }
    });

  } catch (error) {
    debugLog('Error al iniciar intento de quiz:', error);
    res.status(500).json({
      success: false,
      message: 'Error al iniciar intento de quiz',
      error: error.message
    });
  }
};

// Enviar respuestas y completar quiz
const submitQuizAttempt = async (req, res) => {
  try {
    const { id } = req.params; // Quiz ID
    const { attemptId, answers } = req.body;
    
    debugLog(`Enviando respuestas para quiz ${id}, intento ${attemptId}`);

    const attempt = await QuizAttempt.findOne({
      where: {
        id: attemptId,
        quizId: id,
        userId: req.user.id,
        status: 'in_progress'
      }
    });

    if (!attempt) {
      return res.status(404).json({
        success: false,
        message: 'Intento de quiz no encontrado o ya completado'
      });
    }

    // Obtener quiz con preguntas y respuestas correctas
    const quiz = await Quiz.findByPk(id, {
      include: [
        {
          model: QuizQuestion,
          as: 'Questions',
          include: [
            {
              model: QuizAnswer,
              as: 'Answers'
            }
          ]
        }
      ]
    });

    // Calcular puntuación
    let totalPoints = 0;
    let maxPoints = 0;
    const results = [];

    for (const question of quiz.Questions) {
      maxPoints += question.points;
      const userAnswer = answers.find(a => a.questionId === question.id);
      
      if (userAnswer) {
        const correctAnswers = question.Answers.filter(a => a.isCorrect);
        const userSelectedAnswers = question.Answers.filter(a => 
          userAnswer.selectedAnswerIds.includes(a.id)
        );
        
        // Lógica simple: todas las respuestas correctas deben estar seleccionadas
        const isCorrect = correctAnswers.length === userSelectedAnswers.length &&
          correctAnswers.every(ca => userSelectedAnswers.some(usa => usa.id === ca.id));
        
        if (isCorrect) {
          totalPoints += question.points;
        }

        results.push({
          questionId: question.id,
          selectedAnswerIds: userAnswer.selectedAnswerIds,
          isCorrect,
          points: isCorrect ? question.points : 0
        });
      }
    }

    const score = maxPoints > 0 ? (totalPoints / maxPoints) * 100 : 0;
    const passed = score >= quiz.passingScore;

    // Actualizar intento
    await attempt.update({
      completedAt: new Date(),
      timeSpent: Math.floor((new Date() - attempt.startedAt) / 1000),
      score,
      totalPoints,
      maxPoints,
      passed,
      status: 'completed',
      answers: JSON.stringify(answers)
    });

    res.json({
      success: true,
      message: 'Quiz completado exitosamente',
      data: {
        score,
        totalPoints,
        maxPoints,
        passed,
        results: quiz.showCorrectAnswers ? results : undefined
      }
    });

  } catch (error) {
    debugLog('Error al enviar respuestas de quiz:', error);
    res.status(500).json({
      success: false,
      message: 'Error al enviar respuestas de quiz',
      error: error.message
    });
  }
};

module.exports = {
  getQuizzes,
  getQuizById,
  createQuiz,
  startQuizAttempt,
  submitQuizAttempt
};
