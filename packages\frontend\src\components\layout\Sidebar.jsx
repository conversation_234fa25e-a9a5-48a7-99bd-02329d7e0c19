import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import useAuth from '../../hooks/useAuth';
import { useDebug } from '../../contexts/DebugContext';
import BumeranLogo from '../common/BumeranLogo';
import './Sidebar.css';
import {
  FiHome,
  FiUser,
  FiUsers,
  FiSettings,
  FiFileText,
  FiCode,
  FiDatabase,
  FiLayers,
  FiBarChart2,
  FiChevronRight,
  FiChevronDown,
  FiChevronLeft,
  FiMenu,
  FiX,
  FiCalendar,
  FiCheckSquare,
  FiBookOpen,
  FiMessageSquare,
  FiShoppingBag,
  FiGlobe,
  FiFolder
} from 'react-icons/fi';

const Sidebar = () => {
  const { currentUser, isAuthenticated } = useAuth();
  const location = useLocation();
  const { debugMode } = useDebug();
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isMobileOpen, setIsMobileOpen] = useState(false);
  const [expandedMenus, setExpandedMenus] = useState({});

  // Obtener el rol desde localStorage como respaldo
  const userFromStorage = JSON.parse(localStorage.getItem('user') || '{}');
  const roleFromStorage = userFromStorage?.role;

  // Verificar el rol del usuario
  const isGlobalAdmin = currentUser?.role === 'GLOBAL_ADMIN' || roleFromStorage === 'GLOBAL_ADMIN';
  const isAcceleratorAdmin = currentUser?.role === 'ACCELERATOR_ADMIN' || roleFromStorage === 'ACCELERATOR_ADMIN';
  const isMentor = currentUser?.role === 'MENTOR' || roleFromStorage === 'MENTOR';
  const isEntrepreneur = currentUser?.role === 'ENTREPRENEUR' || roleFromStorage === 'ENTREPRENEUR';

  // Determinar si una ruta está activa
  const isActive = (path) => {
    return location.pathname === path || location.pathname.startsWith(`${path}/`);
  };

  // Alternar la expansión de un menú
  const toggleMenu = (menuId) => {
    setExpandedMenus(prev => ({
      ...prev,
      [menuId]: !prev[menuId]
    }));
  };

  // Alternar el colapso del sidebar
  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  // Alternar la apertura del sidebar en móviles
  const toggleMobile = () => {
    setIsMobileOpen(!isMobileOpen);
  };

  // Cerrar el sidebar en móviles cuando cambia la ruta
  useEffect(() => {
    setIsMobileOpen(false);
  }, [location.pathname]);

  // Si no está autenticado, no mostrar el sidebar
  if (!isAuthenticated) {
    return null;
  }

  // Clases para los elementos del menú
  const menuItemClass = (active) => `
    flex items-center p-2 rounded-md
    ${active ? 'bg-blue-700 text-white' : 'text-gray-700 hover:bg-blue-50 hover:text-blue-700'}
    ${isCollapsed ? 'justify-center' : ''}
    transition-colors duration-200
  `;

  const menuTextClass = `
    ${isCollapsed ? 'hidden' : 'ml-3'}
    transition-all duration-200
  `;

  const submenuItemClass = (active) => `
    flex items-center p-2 pl-${isCollapsed ? '2' : '8'} rounded-md
    ${active ? 'bg-blue-700 text-white' : 'text-gray-700 hover:bg-blue-50 hover:text-blue-700'}
    transition-colors duration-200
  `;

  // Botón para móviles (fuera del sidebar)
  const mobileButton = (
    <button
      onClick={toggleMobile}
      className="fixed bottom-4 right-4 z-30 p-3 bg-blue-600 text-white rounded-full shadow-lg md:hidden"
    >
      {isMobileOpen ? <FiX size={24} /> : <FiMenu size={24} />}
    </button>
  );

  // Contenido del sidebar
  const sidebarContent = (
    <>
      {/* Logo de Bumeran */}
      <div className={`flex ${isCollapsed ? 'justify-center' : 'justify-between'} items-center p-2 border-b border-gray-200`}>
        <Link to="/" className="flex items-center">
          <BumeranLogo className={isCollapsed ? "" : "mr-2"} size={isCollapsed ? "sm" : "sm"} />
          {!isCollapsed && <span className="text-base font-bold text-blue-700">Bumeran</span>}
        </Link>

        {/* Botón para colapsar/expandir (solo en desktop) */}
        {!isCollapsed && (
          <button
            onClick={toggleCollapse}
            className="p-1 rounded-md text-gray-500 hover:bg-gray-100"
          >
            <FiChevronLeft size={16} />
          </button>
        )}
        {isCollapsed && (
          <button
            onClick={toggleCollapse}
            className="mt-2 p-1 rounded-md text-gray-500 hover:bg-gray-100"
          >
            <FiChevronRight size={16} />
          </button>
        )}
      </div>

      {/* Menú de navegación */}
      <nav className="mt-2 px-2">
        <ul className="space-y-1">
          {/* Common Dashboard and Mi Perfil links removed */}

          {/* Menú para Administrador Global */}
          {isGlobalAdmin && (
            <>
              <li className="mt-4">
                <div className={`${isCollapsed ? 'hidden' : 'px-2 py-1 text-xs font-semibold text-gray-500 uppercase tracking-wider'}`}>
                  Menú
                </div>
              </li>

              {/* Dashboard Link */}
              <li>
                <Link
                  to="/admin/dashboard"
                  className={menuItemClass(isActive('/admin/dashboard'))}
                >
                  <FiHome size={20} />
                  <span className={menuTextClass}>Dashboard</span>
                </Link>
              </li>

              {/* Mi Perfil Link */}
              <li>
                <Link
                  to="/profile"
                  className={menuItemClass(isActive('/profile'))}
                >
                  <FiUser size={20} />
                  <span className={menuTextClass}>Mi Perfil</span>
                </Link>
              </li>

              {/* Usuarios y Roles */}
              <li>
                <Link
                  to="/admin/users"
                  className={menuItemClass(isActive('/admin/users'))}
                >
                  <FiUsers size={20} />
                  <span className={menuTextClass}>Usuarios y Roles</span>
                </Link>
              </li>

              {/* Aceleradoras */}
              <li>
                <Link
                  to="/admin/accelerators"
                  className={menuItemClass(isActive('/admin/accelerators'))}
                >
                  <FiBarChart2 size={20} />
                  <span className={menuTextClass}>Aceleradoras</span>
                </Link>
              </li>

              {/* Crear Programa */}
              <li>
                <Link
                  to="/admin/programs"
                  className={menuItemClass(isActive('/admin/programs'))}
                >
                  <FiFolder size={20} />
                  <span className={menuTextClass}>Crear Programa</span>
                </Link>
              </li>

              {/* Códigos de registro */}
              <li>
                <Link
                  to="/admin/registration-codes"
                  className={menuItemClass(isActive('/admin/registration-codes'))}
                >
                  <FiCode size={20} />
                  <span className={menuTextClass}>Códigos de registro</span>
                </Link>
              </li>

              {/* Formularios */}
              <li>
                <Link
                  to="/admin/forms"
                  className={menuItemClass(isActive('/admin/forms'))}
                >
                  <FiFileText size={20} />
                  <span className={menuTextClass}>Formularios</span>
                </Link>
              </li>

              {/* Aplicaciones */}
              <li>
                <Link
                  to="/admin/applications"
                  className={menuItemClass(isActive('/admin/applications'))}
                >
                  <FiLayers size={20} />
                  <span className={menuTextClass}>Aplicaciones</span>
                </Link>
              </li>

              {/* Eventos */}
              <li>
                <Link
                  to="/admin/events"
                  className={menuItemClass(isActive('/admin/events'))}
                >
                  <FiCalendar size={20} />
                  <span className={menuTextClass}>Eventos</span>
                </Link>
              </li>

              {/* Recursos */}
              <li>
                <Link
                  to="/admin/resources"
                  className={menuItemClass(isActive('/admin/resources'))}
                >
                  <FiFolder size={20} />
                  <span className={menuTextClass}>Recursos</span>
                </Link>
              </li>

              {/* Mentores */}
              <li>
                <Link
                  to="/admin/mentors"
                  className={menuItemClass(isActive('/admin/mentors'))}
                >
                  <FiUser size={20} />
                  <span className={menuTextClass}>Mentores</span>
                </Link>
              </li>

              {/* Comunidad */}
              <li>
                <Link
                  to="/admin/community"
                  className={menuItemClass(isActive('/admin/community'))}
                >
                  <FiMessageSquare size={20} />
                  <span className={menuTextClass}>Comunidad</span>
                </Link>
              </li>

              {/* Avisos */}
              <li>
                <Link
                  to="/admin/announcements"
                  className={menuItemClass(isActive('/admin/announcements'))}
                >
                  <FiMessageSquare size={20} />
                  <span className={menuTextClass}>Avisos</span>
                </Link>
              </li>

              {/* LMS - Cursos */}
              <li>
                <button
                  onClick={() => toggleMenu('lms')}
                  className={`${menuItemClass(isActive('/admin/courses') || isActive('/student/courses'))} w-full justify-between`}
                >
                  <div className="flex items-center">
                    <FiBookOpen size={20} />
                    <span className={menuTextClass}>LMS - Cursos</span>
                  </div>
                  {!isCollapsed && (
                    expandedMenus.lms ? <FiChevronDown size={16} /> : <FiChevronRight size={16} />
                  )}
                </button>
                {(expandedMenus.lms || isCollapsed) && (
                  <ul className={`${isCollapsed ? 'hidden' : 'mt-1 space-y-1'}`}>
                    <li>
                      <Link
                        to="/admin/courses"
                        className={submenuItemClass(isActive('/admin/courses'))}
                      >
                        <FiSettings size={16} />
                        <span className={menuTextClass}>Gestionar Cursos</span>
                      </Link>
                    </li>
                    <li>
                      <Link
                        to="/student/courses"
                        className={submenuItemClass(isActive('/student/courses'))}
                      >
                        <FiUser size={16} />
                        <span className={menuTextClass}>Ver como Estudiante</span>
                      </Link>
                    </li>
                  </ul>
                )}
              </li>

              {/* Tareas */}
              <li>
                <Link
                  to="/admin/tasks"
                  className={menuItemClass(isActive('/admin/tasks'))}
                >
                  <FiCheckSquare size={20} />
                  <span className={menuTextClass}>Tareas</span>
                </Link>
              </li>

              {/* Calendario */}
              <li>
                <Link
                  to="/admin/calendar"
                  className={menuItemClass(isActive('/admin/calendar'))}
                >
                  <FiCalendar size={20} />
                  <span className={menuTextClass}>Calendario</span>
                </Link>
              </li>

              {/* Startups */}
              <li>
                <Link
                  to="/admin/startups"
                  className={menuItemClass(isActive('/admin/startups'))}
                >
                  <FiGlobe size={20} />
                  <span className={menuTextClass}>Startups</span>
                </Link>
              </li>

              {/* Marketplace */}
              <li>
                <Link
                  to="/admin/marketplace"
                  className={menuItemClass(isActive('/admin/marketplace'))}
                >
                  <FiShoppingBag size={20} />
                  <span className={menuTextClass}>Marketplace</span>
                </Link>
              </li>

              {/* Configuración */}
              <li>
                <Link
                  to="/admin/settings"
                  className={menuItemClass(isActive('/admin/settings'))}
                >
                  <FiSettings size={20} />
                  <span className={menuTextClass}>Configuración</span>
                </Link>
              </li>

              {/* Sección de Coaching y Mentoría */}
              <li className="mt-4">
                <div className={`${isCollapsed ? 'hidden' : 'px-2 py-1 text-xs font-semibold text-gray-500 uppercase tracking-wider'}`}>
                  Coaching y Mentoría
                </div>
              </li>

              {/* Dashboard de Coaching */}
              <li>
                <Link
                  to="/admin/coaching"
                  className={menuItemClass(isActive('/admin/coaching'))}
                >
                  <FiBarChart2 size={20} />
                  <span className={menuTextClass}>Dashboard Coaching</span>
                </Link>
              </li>

              {/* Sesiones */}
              <li>
                <Link
                  to="/admin/sessions"
                  className={menuItemClass(isActive('/admin/sessions'))}
                >
                  <FiUsers size={20} />
                  <span className={menuTextClass}>Sesiones</span>
                </Link>
              </li>

              {/* Tareas */}
              <li>
                <Link
                  to="/admin/tasks"
                  className={menuItemClass(isActive('/admin/tasks'))}
                >
                  <FiCheckSquare size={20} />
                  <span className={menuTextClass}>Tareas</span>
                </Link>
              </li>

              {/* Notas de Sesión */}
              <li>
                <Link
                  to="/admin/session-notes"
                  className={menuItemClass(isActive('/admin/session-notes'))}
                >
                  <FiMessageSquare size={20} />
                  <span className={menuTextClass}>Notas de Sesión</span>
                </Link>
              </li>
            </>
          )}

          {/* Menú para Administrador de Aceleradora */}
          {isAcceleratorAdmin && (
            <>
              <li className="mt-4">
                <div className={`${isCollapsed ? 'hidden' : 'px-2 py-1 text-xs font-semibold text-gray-500 uppercase tracking-wider'}`}>
                  Menú
                </div>
              </li>

              {/* Aceleradora (Dashboard Equivalent) */}
              <li>
                <Link
                  to="/accelerator/dashboard"
                  className={menuItemClass(isActive('/accelerator/dashboard'))}
                >
                  <FiBarChart2 size={20} />
                  <span className={menuTextClass}>Mi Aceleradora</span>
                </Link>
              </li>

              {/* Mi Perfil Link */}
              <li>
                <Link
                  to="/profile"
                  className={menuItemClass(isActive('/profile'))}
                >
                  <FiUser size={20} />
                  <span className={menuTextClass}>Mi Perfil</span>
                </Link>
              </li>

              {/* Programas */}
              <li>
                <Link
                  to="/accelerator/programs"
                  className={menuItemClass(isActive('/accelerator/programs'))}
                >
                  <FiFolder size={20} />
                  <span className={menuTextClass}>Programas</span>
                </Link>
              </li>

              {/* Usuarios */}
              <li>
                <Link
                  to="/accelerator/users"
                  className={menuItemClass(isActive('/accelerator/users'))}
                >
                  <FiUsers size={20} />
                  <span className={menuTextClass}>Usuarios</span>
                </Link>
              </li>

              {/* Códigos de registro */}
              <li>
                <Link
                  to="/accelerator/registration-codes"
                  className={menuItemClass(isActive('/accelerator/registration-codes'))}
                >
                  <FiCode size={20} />
                  <span className={menuTextClass}>Códigos de registro</span>
                </Link>
              </li>

              {/* Formularios */}
              <li>
                <Link
                  to="/accelerator/forms"
                  className={menuItemClass(isActive('/accelerator/forms'))}
                >
                  <FiFileText size={20} />
                  <span className={menuTextClass}>Formularios</span>
                </Link>
              </li>

              {/* Aplicaciones */}
              <li>
                <Link
                  to="/accelerator/applications"
                  className={menuItemClass(isActive('/accelerator/applications'))}
                >
                  <FiLayers size={20} />
                  <span className={menuTextClass}>Aplicaciones</span>
                </Link>
              </li>

              {/* Eventos */}
              <li>
                <Link
                  to="/accelerator/events"
                  className={menuItemClass(isActive('/accelerator/events'))}
                >
                  <FiCalendar size={20} />
                  <span className={menuTextClass}>Eventos</span>
                </Link>
              </li>

              {/* Recursos */}
              <li>
                <Link
                  to="/accelerator/resources"
                  className={menuItemClass(isActive('/accelerator/resources'))}
                >
                  <FiFolder size={20} />
                  <span className={menuTextClass}>Recursos</span>
                </Link>
              </li>

              {/* Mentores */}
              <li>
                <Link
                  to="/accelerator/mentors"
                  className={menuItemClass(isActive('/accelerator/mentors'))}
                >
                  <FiUser size={20} />
                  <span className={menuTextClass}>Mentores</span>
                </Link>
              </li>

              {/* Startups */}
              <li>
                <Link
                  to="/accelerator/startups"
                  className={menuItemClass(isActive('/accelerator/startups'))}
                >
                  <FiGlobe size={20} />
                  <span className={menuTextClass}>Startups</span>
                </Link>
              </li>

              {/* Comunidad */}
              <li>
                <Link
                  to="/accelerator/community"
                  className={menuItemClass(isActive('/accelerator/community'))}
                >
                  <FiMessageSquare size={20} />
                  <span className={menuTextClass}>Comunidad</span>
                </Link>
              </li>

              {/* Avisos */}
              <li>
                <Link
                  to="/accelerator/announcements"
                  className={menuItemClass(isActive('/accelerator/announcements'))}
                >
                  <FiMessageSquare size={20} />
                  <span className={menuTextClass}>Avisos</span>
                </Link>
              </li>

              {/* Calendario */}
              <li>
                <Link
                  to="/accelerator/calendar"
                  className={menuItemClass(isActive('/accelerator/calendar'))}
                >
                  <FiCalendar size={20} />
                  <span className={menuTextClass}>Calendario</span>
                </Link>
              </li>

              {/* Configuración */}
              <li>
                <Link
                  to="/accelerator/settings"
                  className={menuItemClass(isActive('/accelerator/settings'))}
                >
                  <FiSettings size={20} />
                  <span className={menuTextClass}>Configuración</span>
                </Link>
              </li>

              {/* Sección de Coaching y Mentoría */}
              <li className="mt-4">
                <div className={`${isCollapsed ? 'hidden' : 'px-2 py-1 text-xs font-semibold text-gray-500 uppercase tracking-wider'}`}>
                  Coaching y Mentoría
                </div>
              </li>

              {/* Dashboard de Coaching */}
              <li>
                <Link
                  to="/admin/coaching"
                  className={menuItemClass(isActive('/admin/coaching'))}
                >
                  <FiBarChart2 size={20} />
                  <span className={menuTextClass}>Dashboard Coaching</span>
                </Link>
              </li>

              {/* Sesiones */}
              <li>
                <Link
                  to="/admin/sessions"
                  className={menuItemClass(isActive('/admin/sessions'))}
                >
                  <FiUsers size={20} />
                  <span className={menuTextClass}>Sesiones</span>
                </Link>
              </li>

              {/* Tareas */}
              <li>
                <Link
                  to="/admin/tasks"
                  className={menuItemClass(isActive('/admin/tasks'))}
                >
                  <FiCheckSquare size={20} />
                  <span className={menuTextClass}>Tareas</span>
                </Link>
              </li>
            </>
          )}

          {/* Menú para Mentor */}
          {isMentor && (
            <>
              <li className="mt-4">
                <div className={`${isCollapsed ? 'hidden' : 'px-2 py-1 text-xs font-semibold text-gray-500 uppercase tracking-wider'}`}>
                  Menú
                </div>
              </li>

              {/* Dashboard de Coaching */}
              <li>
                <Link
                  to="/admin/coaching"
                  className={menuItemClass(isActive('/admin/coaching'))}
                >
                  <FiHome size={20} />
                  <span className={menuTextClass}>Dashboard Coaching</span>
                </Link>
              </li>

              {/* Mi Perfil Link */}
              <li>
                <Link
                  to="/profile"
                  className={menuItemClass(isActive('/profile'))}
                >
                  <FiUser size={20} />
                  <span className={menuTextClass}>Mi Perfil</span>
                </Link>
              </li>

              {/* Sesiones de mentoría */}
              <li>
                <Link
                  to="/admin/sessions"
                  className={menuItemClass(isActive('/admin/sessions'))}
                >
                  <FiUsers size={20} />
                  <span className={menuTextClass}>Mis Sesiones</span>
                </Link>
              </li>

              {/* Tareas */}
              <li>
                <Link
                  to="/admin/tasks"
                  className={menuItemClass(isActive('/admin/tasks'))}
                >
                  <FiCheckSquare size={20} />
                  <span className={menuTextClass}>Tareas</span>
                </Link>
              </li>

              {/* Notas de Sesión */}
              <li>
                <Link
                  to="/admin/session-notes"
                  className={menuItemClass(isActive('/admin/session-notes'))}
                >
                  <FiMessageSquare size={20} />
                  <span className={menuTextClass}>Notas</span>
                </Link>
              </li>

              {/* Aplicaciones */}
              <li>
                <Link
                  to="/mentor/applications"
                  className={menuItemClass(isActive('/mentor/applications'))}
                >
                  <FiLayers size={20} />
                  <span className={menuTextClass}>Aplicaciones</span>
                </Link>
              </li>

              {/* Tareas */}
              <li>
                <Link
                  to="/mentor/tasks"
                  className={menuItemClass(isActive('/mentor/tasks'))}
                >
                  <FiCheckSquare size={20} />
                  <span className={menuTextClass}>Tareas</span>
                </Link>
              </li>

              {/* Calendario */}
              <li>
                <Link
                  to="/mentor/calendar"
                  className={menuItemClass(isActive('/mentor/calendar'))}
                >
                  <FiCalendar size={20} />
                  <span className={menuTextClass}>Calendario</span>
                </Link>
              </li>

              {/* Recursos */}
              <li>
                <Link
                  to="/mentor/resources"
                  className={menuItemClass(isActive('/mentor/resources'))}
                >
                  <FiFolder size={20} />
                  <span className={menuTextClass}>Recursos</span>
                </Link>
              </li>

              {/* Comunidad */}
              <li>
                <Link
                  to="/mentor/community"
                  className={menuItemClass(isActive('/mentor/community'))}
                >
                  <FiMessageSquare size={20} />
                  <span className={menuTextClass}>Comunidad</span>
                </Link>
              </li>

              {/* LMS - Cursos */}
              <li>
                <Link
                  to="/admin/courses"
                  className={menuItemClass(isActive('/admin/courses'))}
                >
                  <FiBookOpen size={20} />
                  <span className={menuTextClass}>Gestionar Cursos</span>
                </Link>
              </li>

              {/* Ver como Estudiante */}
              <li>
                <Link
                  to="/student/courses"
                  className={menuItemClass(isActive('/student/courses'))}
                >
                  <FiUser size={20} />
                  <span className={menuTextClass}>Mis Cursos</span>
                </Link>
              </li>
            </>
          )}

          {/* Menú para Emprendedor */}
          {isEntrepreneur && (
            <>
              <li className="mt-4">
                <div className={`${isCollapsed ? 'hidden' : 'px-2 py-1 text-xs font-semibold text-gray-500 uppercase tracking-wider'}`}>
                  Menú
                </div>
              </li>

              {/* Dashboard de Coaching */}
              <li>
                <Link
                  to="/admin/coaching"
                  className={menuItemClass(isActive('/admin/coaching'))}
                >
                  <FiHome size={20} />
                  <span className={menuTextClass}>Dashboard</span>
                </Link>
              </li>

              {/* Mi Perfil Link */}
              <li>
                <Link
                  to="/profile"
                  className={menuItemClass(isActive('/profile'))}
                >
                  <FiUser size={20} />
                  <span className={menuTextClass}>Mi Perfil</span>
                </Link>
              </li>

              {/* Sesiones */}
              <li>
                <Link
                  to="/admin/sessions"
                  className={menuItemClass(isActive('/admin/sessions'))}
                >
                  <FiUsers size={20} />
                  <span className={menuTextClass}>Mis Sesiones</span>
                </Link>
              </li>

              {/* Tareas */}
              <li>
                <Link
                  to="/admin/tasks"
                  className={menuItemClass(isActive('/admin/tasks'))}
                >
                  <FiCheckSquare size={20} />
                  <span className={menuTextClass}>Mis Tareas</span>
                </Link>
              </li>

              {/* Notas de Sesión */}
              <li>
                <Link
                  to="/admin/session-notes"
                  className={menuItemClass(isActive('/admin/session-notes'))}
                >
                  <FiMessageSquare size={20} />
                  <span className={menuTextClass}>Notas</span>
                </Link>
              </li>

              {/* Aplicaciones */}
              <li>
                <Link
                  to="/startup/applications"
                  className={menuItemClass(isActive('/startup/applications'))}
                >
                  <FiLayers size={20} />
                  <span className={menuTextClass}>Aplicaciones</span>
                </Link>
              </li>

              {/* Calendario */}
              <li>
                <Link
                  to="/startup/calendar"
                  className={menuItemClass(isActive('/startup/calendar'))}
                >
                  <FiCalendar size={20} />
                  <span className={menuTextClass}>Calendario</span>
                </Link>
              </li>

              {/* Recursos */}
              <li>
                <Link
                  to="/startup/resources"
                  className={menuItemClass(isActive('/startup/resources'))}
                >
                  <FiFolder size={20} />
                  <span className={menuTextClass}>Recursos</span>
                </Link>
              </li>

              {/* Cursos */}
              <li>
                <Link
                  to="/student/courses"
                  className={menuItemClass(isActive('/student/courses'))}
                >
                  <FiBookOpen size={20} />
                  <span className={menuTextClass}>Mis Cursos</span>
                </Link>
              </li>

              {/* Comunidad */}
              <li>
                <Link
                  to="/startup/community"
                  className={menuItemClass(isActive('/startup/community'))}
                >
                  <FiMessageSquare size={20} />
                  <span className={menuTextClass}>Comunidad</span>
                </Link>
              </li>

              {/* Marketplace */}
              <li>
                <Link
                  to="/startup/marketplace"
                  className={menuItemClass(isActive('/startup/marketplace'))}
                >
                  <FiShoppingBag size={20} />
                  <span className={menuTextClass}>Marketplace</span>
                </Link>
              </li>
            </>
          )}

          {/* Herramientas de debug */}
          {debugMode && (
            <>
              <li className="mt-4">
                <div className={`${isCollapsed ? 'hidden' : 'px-2 py-1 text-xs font-semibold text-gray-500 uppercase tracking-wider'}`}>
                  Desarrollo
                </div>
              </li>
              <li>
                <Link
                  to="/debug/auth"
                  className={menuItemClass(isActive('/debug/auth'))}
                >
                  <FiSettings size={20} />
                  <span className={menuTextClass}>Herramientas de Debug</span>
                </Link>
              </li>
            </>
          )}
        </ul>
      </nav>
    </>
  );

  return (
    <>
      {/* Sidebar para desktop */}
      <aside className={`
        hidden md:block fixed top-12 left-0 h-[calc(100%-3rem)] bg-white shadow-md z-20
        transition-all duration-300 ease-in-out
        ${isCollapsed ? 'w-16' : 'w-64'}
        overflow-y-auto
      `}>
        {sidebarContent}
      </aside>

      {/* Sidebar para móviles */}
      <aside className={`
        md:hidden fixed top-12 left-0 h-[calc(100%-3rem)] bg-white shadow-md z-20
        transition-all duration-300 ease-in-out
        ${isMobileOpen ? 'w-64 translate-x-0' : 'w-64 -translate-x-full'}
        overflow-y-auto
      `}>
        {sidebarContent}
      </aside>

      {/* Overlay para móviles */}
      {isMobileOpen && (
        <div
          className="md:hidden fixed inset-0 bg-black bg-opacity-50 z-10"
          onClick={toggleMobile}
        />
      )}

      {/* Botón para móviles */}
      {mobileButton}
    </>
  );
};

export default Sidebar;
