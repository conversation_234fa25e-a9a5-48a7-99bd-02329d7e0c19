const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

// Colores para la consola
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function logSuccess(message) {
  console.log(`${colors.green}✅ ${message}${colors.reset}`);
}

function logError(message) {
  console.log(`${colors.red}❌ ${message}${colors.reset}`);
}

function logWarning(message) {
  console.log(`${colors.yellow}⚠️ ${message}${colors.reset}`);
}

function logInfo(message) {
  console.log(`${colors.cyan}ℹ️ ${message}${colors.reset}`);
}

function logHeader(message) {
  console.log(`\n${colors.blue}${colors.bright}🔍 === ${message} ===${colors.reset}`);
}

let authToken = '';

function getAuthHeaders() {
  return {
    'Authorization': `Bearer ${authToken}`,
    'Content-Type': 'application/json'
  };
}

async function login() {
  try {
    const response = await axios.post(`${BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });

    if (response.data.success) {
      authToken = response.data.token;
      logSuccess('Login exitoso');
      return true;
    } else {
      logError('Login fallido');
      return false;
    }
  } catch (error) {
    logError(`Error en login: ${error.message}`);
    return false;
  }
}

async function debugQuizCreationWithQuestions() {
  logHeader('DEBUG: Creación de Quiz con Preguntas');
  
  try {
    // Primero crear un curso simple
    const courseData = {
      title: 'Curso Debug',
      description: 'Curso para debug',
      difficulty: 'beginner',
      category: 'Debug'
    };

    const courseResponse = await axios.post(`${BASE_URL}/courses`, courseData, {
      headers: getAuthHeaders()
    });

    if (courseResponse.data.success) {
      const courseId = courseResponse.data.data.id;
      logSuccess(`Curso creado: ID ${courseId}`);
      
      // Intentar crear quiz SIN preguntas primero
      const simpleQuizData = {
        title: 'Quiz Simple',
        description: 'Quiz sin preguntas',
        courseId: courseId,
        timeLimit: 30,
        maxAttempts: 3,
        passingScore: 70
      };

      const simpleQuizResponse = await axios.post(`${BASE_URL}/quizzes`, simpleQuizData, {
        headers: getAuthHeaders()
      });

      if (simpleQuizResponse.data.success) {
        logSuccess('Quiz simple creado exitosamente');
        
        // Ahora intentar crear quiz CON preguntas
        const complexQuizData = {
          title: 'Quiz Complejo',
          description: 'Quiz con preguntas',
          courseId: courseId,
          timeLimit: 30,
          maxAttempts: 3,
          passingScore: 70,
          questions: [
            {
              question: '¿Pregunta de prueba?',
              type: 'multiple_choice',
              points: 50,
              answers: [
                { text: 'Respuesta correcta', isCorrect: true },
                { text: 'Respuesta incorrecta', isCorrect: false }
              ]
            }
          ]
        };

        try {
          const complexQuizResponse = await axios.post(`${BASE_URL}/quizzes`, complexQuizData, {
            headers: getAuthHeaders()
          });
          
          if (complexQuizResponse.data.success) {
            logSuccess('Quiz con preguntas creado exitosamente');
          } else {
            logError('Fallo al crear quiz con preguntas');
            logInfo(`Error: ${JSON.stringify(complexQuizResponse.data, null, 2)}`);
          }
        } catch (error) {
          logError('Error al crear quiz con preguntas');
          logInfo(`Detalles del error: ${error.response?.data?.message || error.message}`);
          if (error.response?.data) {
            logInfo(`Respuesta completa: ${JSON.stringify(error.response.data, null, 2)}`);
          }
        }
      } else {
        logError('Fallo al crear quiz simple');
      }
    } else {
      logError('Fallo al crear curso');
    }
  } catch (error) {
    logError(`Error general: ${error.message}`);
  }
}

async function debugDuplicateEnrollment() {
  logHeader('DEBUG: Inscripción Duplicada');
  
  try {
    // Obtener cursos disponibles
    const coursesResponse = await axios.get(`${BASE_URL}/courses`, {
      headers: getAuthHeaders()
    });

    if (coursesResponse.data.success && coursesResponse.data.data.length > 0) {
      const course = coursesResponse.data.data[0];
      logInfo(`Intentando inscripción en: ${course.title}`);
      
      // Primera inscripción
      try {
        const firstEnrollment = await axios.post(`${BASE_URL}/enrollments/course/${course.id}`, {}, {
          headers: getAuthHeaders()
        });
        
        if (firstEnrollment.data.success) {
          logSuccess('Primera inscripción exitosa');
          
          // Segunda inscripción (debería fallar o manejar duplicado)
          try {
            const secondEnrollment = await axios.post(`${BASE_URL}/enrollments/course/${course.id}`, {}, {
              headers: getAuthHeaders()
            });
            
            if (secondEnrollment.data.success) {
              logWarning('Segunda inscripción también exitosa - posible duplicado');
            } else {
              logInfo('Segunda inscripción rechazada correctamente');
            }
          } catch (error) {
            logInfo('Segunda inscripción rechazada con error (esperado)');
            logInfo(`Mensaje: ${error.response?.data?.message || error.message}`);
          }
        } else {
          logError('Primera inscripción falló');
        }
      } catch (error) {
        logInfo('Usuario ya inscrito en este curso');
        logInfo(`Mensaje: ${error.response?.data?.message || error.message}`);
      }
    } else {
      logError('No hay cursos disponibles');
    }
  } catch (error) {
    logError(`Error general: ${error.message}`);
  }
}

async function debugQuizSubmission() {
  logHeader('DEBUG: Envío de Respuestas de Quiz');
  
  try {
    // Obtener quizzes disponibles
    const quizzesResponse = await axios.get(`${BASE_URL}/quizzes`, {
      headers: getAuthHeaders()
    });

    if (quizzesResponse.data.success && quizzesResponse.data.data.length > 0) {
      const quiz = quizzesResponse.data.data[0];
      logInfo(`Quiz seleccionado: ${quiz.title}`);
      logInfo(`Preguntas disponibles: ${quiz.QuizQuestions?.length || 0}`);
      
      // Iniciar intento
      try {
        const attemptResponse = await axios.post(`${BASE_URL}/quizzes/${quiz.id}/attempt`, {}, {
          headers: getAuthHeaders()
        });

        if (attemptResponse.data.success) {
          const attemptId = attemptResponse.data.data.attemptId;
          logSuccess(`Intento iniciado: ${attemptId}`);
          
          // Preparar respuestas
          const answers = {};
          if (quiz.QuizQuestions && quiz.QuizQuestions.length > 0) {
            logInfo('Preparando respuestas...');
            quiz.QuizQuestions.forEach((question, index) => {
              logInfo(`Pregunta ${index + 1}: ${question.question}`);
              if (question.QuizAnswers && question.QuizAnswers.length > 0) {
                const firstAnswer = question.QuizAnswers[0];
                answers[question.id] = firstAnswer.id;
                logInfo(`Respuesta seleccionada: ${firstAnswer.text}`);
              }
            });
          } else {
            logWarning('No hay preguntas en este quiz');
          }
          
          // Enviar respuestas
          const submitData = {
            attemptId: attemptId,
            answers: answers
          };
          
          logInfo(`Datos a enviar: ${JSON.stringify(submitData, null, 2)}`);
          
          try {
            const submitResponse = await axios.post(`${BASE_URL}/quizzes/${quiz.id}/submit`, submitData, {
              headers: getAuthHeaders()
            });

            if (submitResponse.data.success) {
              logSuccess('Respuestas enviadas exitosamente');
              logInfo(`Resultado: ${JSON.stringify(submitResponse.data.data, null, 2)}`);
            } else {
              logError('Fallo al enviar respuestas');
              logInfo(`Error: ${JSON.stringify(submitResponse.data, null, 2)}`);
            }
          } catch (error) {
            logError('Error al enviar respuestas');
            logInfo(`Detalles: ${error.response?.data?.message || error.message}`);
            if (error.response?.data) {
              logInfo(`Respuesta completa: ${JSON.stringify(error.response.data, null, 2)}`);
            }
          }
        } else {
          logError('Fallo al iniciar intento');
        }
      } catch (error) {
        logError('Error al iniciar intento');
        logInfo(`Detalles: ${error.response?.data?.message || error.message}`);
      }
    } else {
      logError('No hay quizzes disponibles');
    }
  } catch (error) {
    logError(`Error general: ${error.message}`);
  }
}

async function runDebugTests() {
  console.log(`${colors.magenta}${colors.bright}🐛 DEBUGGING PROBLEMAS DEL SPRINT 8${colors.reset}`);
  console.log('='.repeat(50));
  
  const loginSuccess = await login();
  
  if (!loginSuccess) {
    console.log(`${colors.red}❌ No se pudo autenticar. Abortando debug.${colors.reset}`);
    return;
  }
  
  await debugQuizCreationWithQuestions();
  await debugDuplicateEnrollment();
  await debugQuizSubmission();
  
  console.log(`\n${colors.bright}🎯 DEBUG COMPLETADO${colors.reset}`);
}

// Ejecutar debug
runDebugTests().catch(console.error);
