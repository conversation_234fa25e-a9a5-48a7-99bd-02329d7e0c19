import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { FiSave, FiArrowLeft } from 'react-icons/fi';
import { createCourse, updateCourse, getCourseById } from '../../services/course.service';

const CourseForm = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [isEditing, setIsEditing] = useState(false);

  const navigate = useNavigate();
  const { id } = useParams();

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset
  } = useForm({
    defaultValues: {
      title: '',
      description: '',
      shortDescription: '',
      thumbnail: '',
      duration: '',
      difficulty: 'beginner',
      category: '',
      tags: '',
      enrollmentLimit: '',
      startDate: '',
      endDate: '',
      passingScore: 70,
      isPublished: false
    }
  });

  useEffect(() => {
    if (id) {
      setIsEditing(true);
      loadCourse();
    }
  }, [id]);

  const loadCourse = async () => {
    try {
      setLoading(true);
      const response = await getCourseById(id);
      if (response.success) {
        const course = response.data;

        // Formatear fechas para el input date
        const formatDate = (dateString) => {
          if (!dateString) return '';
          return new Date(dateString).toISOString().split('T')[0];
        };

        reset({
          title: course.title || '',
          description: course.description || '',
          shortDescription: course.shortDescription || '',
          thumbnail: course.thumbnail || '',
          duration: course.duration || '',
          difficulty: course.difficulty || 'beginner',
          category: course.category || '',
          tags: course.tags || '',
          enrollmentLimit: course.enrollmentLimit || '',
          startDate: formatDate(course.startDate),
          endDate: formatDate(course.endDate),
          passingScore: course.passingScore || 70,
          isPublished: course.isPublished || false
        });
      }
    } catch (err) {
      setError('Error al cargar curso: ' + (err.message || 'Error desconocido'));
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (data) => {
    try {
      setLoading(true);
      setError('');

      // Convertir campos numéricos
      const courseData = {
        ...data,
        duration: data.duration ? parseInt(data.duration) : null,
        enrollmentLimit: data.enrollmentLimit ? parseInt(data.enrollmentLimit) : null,
        passingScore: parseInt(data.passingScore),
        startDate: data.startDate || null,
        endDate: data.endDate || null
      };

      let response;
      if (isEditing) {
        response = await updateCourse(id, courseData);
      } else {
        response = await createCourse(courseData);
      }

      if (response.success) {
        navigate('/admin/courses');
      }
    } catch (err) {
      setError(err.message || 'Error al guardar curso');
    } finally {
      setLoading(false);
    }
  };

  const categories = [
    'Emprendimiento',
    'Marketing',
    'Finanzas',
    'Tecnología',
    'Liderazgo',
    'Ventas',
    'Recursos Humanos',
    'Operaciones',
    'Legal',
    'Innovación'
  ];

  if (loading && isEditing) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <button
          onClick={() => navigate('/admin/courses')}
          className="p-2 text-gray-600 hover:text-gray-900 rounded-lg hover:bg-gray-100"
        >
          <FiArrowLeft className="w-5 h-5" />
        </button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            {isEditing ? 'Editar Curso' : 'Nuevo Curso'}
          </h1>
          <p className="text-gray-600">
            {isEditing ? 'Modifica los datos del curso' : 'Completa la información del nuevo curso'}
          </p>
        </div>
      </div>

      {/* Error */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          {error}
        </div>
      )}

      {/* Formulario */}
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Información Básica</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Título */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Título del Curso *
              </label>
              <input
                type="text"
                {...register('title', {
                  required: 'El título es requerido',
                  minLength: { value: 3, message: 'El título debe tener al menos 3 caracteres' }
                })}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Ej: Introducción al Emprendimiento"
              />
              {errors.title && (
                <p className="text-red-600 text-sm mt-1">{errors.title.message}</p>
              )}
            </div>

            {/* Descripción corta */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Descripción Corta
              </label>
              <input
                type="text"
                {...register('shortDescription')}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Descripción breve que aparecerá en las tarjetas"
                maxLength={200}
              />
            </div>

            {/* Categoría */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Categoría
              </label>
              <select
                {...register('category')}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Seleccionar categoría</option>
                {categories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
            </div>

            {/* Dificultad */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Dificultad
              </label>
              <select
                {...register('difficulty')}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="beginner">Principiante</option>
                <option value="intermediate">Intermedio</option>
                <option value="advanced">Avanzado</option>
              </select>
            </div>

            {/* Duración */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Duración (minutos)
              </label>
              <input
                type="number"
                {...register('duration', { min: 1 })}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="120"
              />
            </div>

            {/* Puntuación mínima */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Puntuación Mínima (%)
              </label>
              <input
                type="number"
                {...register('passingScore', {
                  required: 'La puntuación mínima es requerida',
                  min: { value: 1, message: 'Debe ser al menos 1%' },
                  max: { value: 100, message: 'No puede ser mayor a 100%' }
                })}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="70"
              />
              {errors.passingScore && (
                <p className="text-red-600 text-sm mt-1">{errors.passingScore.message}</p>
              )}
            </div>

            {/* Tags */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Etiquetas
              </label>
              <input
                type="text"
                {...register('tags')}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="emprendimiento, startup, negocio (separadas por comas)"
              />
              <p className="text-gray-500 text-sm mt-1">
                Separa las etiquetas con comas para mejorar la búsqueda
              </p>
            </div>
          </div>
        </div>

        {/* Descripción completa */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Descripción Completa</h2>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Descripción *
            </label>
            <textarea
              {...register('description', {
                required: 'La descripción es requerida',
                minLength: { value: 10, message: 'La descripción debe tener al menos 10 caracteres' }
              })}
              rows={6}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Describe detalladamente el contenido del curso, objetivos de aprendizaje, y qué aprenderán los estudiantes..."
            />
            {errors.description && (
              <p className="text-red-600 text-sm mt-1">{errors.description.message}</p>
            )}
          </div>
        </div>

        {/* Configuración avanzada */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Configuración Avanzada</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Límite de inscripciones */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Límite de Inscripciones
              </label>
              <input
                type="number"
                {...register('enrollmentLimit', { min: 1 })}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Dejar vacío para ilimitado"
              />
            </div>

            {/* Imagen de portada */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                URL de Imagen de Portada
              </label>
              <input
                type="url"
                {...register('thumbnail')}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="https://ejemplo.com/imagen.jpg"
              />
            </div>

            {/* Fecha de inicio */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Fecha de Inicio
              </label>
              <input
                type="date"
                {...register('startDate')}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Fecha de fin */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Fecha de Finalización
              </label>
              <input
                type="date"
                {...register('endDate')}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Estado de publicación */}
            <div className="md:col-span-2">
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  {...register('isPublished')}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm font-medium text-gray-700">
                  Publicar curso (los estudiantes podrán inscribirse)
                </span>
              </label>
            </div>
          </div>
        </div>

        {/* Botones */}
        <div className="flex justify-end gap-4">
          <button
            type="button"
            onClick={() => navigate('/admin/courses')}
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Cancelar
          </button>
          <button
            type="submit"
            disabled={loading}
            className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg flex items-center gap-2 transition-colors disabled:opacity-50"
          >
            {loading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <FiSave className="w-4 h-4" />
            )}
            {isEditing ? 'Actualizar Curso' : 'Crear Curso'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default CourseForm;
