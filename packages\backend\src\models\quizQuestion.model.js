const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * Modelo QuizQuestion - Representa una pregunta de quiz
 * 
 * Las preguntas pueden ser de diferentes tipos: opción múltiple,
 * verdadero/falso, respuesta corta, etc.
 */
const QuizQuestion = sequelize.define('QuizQuestion', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  quizId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Quizzes',
      key: 'id',
    },
  },
  question: {
    type: DataTypes.TEXT,
    allowNull: false,
    validate: {
      notEmpty: true,
    },
  },
  type: {
    type: DataTypes.ENUM('multiple_choice', 'true_false', 'short_answer', 'essay', 'fill_blank'),
    allowNull: false,
    defaultValue: 'multiple_choice',
  },
  orderIndex: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: 'Orden de la pregunta dentro del quiz',
  },
  points: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 1,
    comment: 'Puntos que vale la pregunta',
  },
  explanation: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Explicación de la respuesta correcta',
  },
  imageUrl: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'URL de imagen asociada a la pregunta',
  },
  isRequired: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
  },
  metadata: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'JSON con configuraciones adicionales',
  },
}, {
  timestamps: true,
  indexes: [
    {
      fields: ['quizId'],
    },
    {
      fields: ['quizId', 'orderIndex'],
    },
    {
      fields: ['type'],
    },
    {
      fields: ['isActive'],
    },
  ],
});

module.exports = QuizQuestion;
