const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * Modelo Enrollment - Representa la inscripción de un usuario a un curso
 * 
 * Registra cuando un usuario se inscribe a un curso y su estado
 * de progreso general en el mismo.
 */
const Enrollment = sequelize.define('Enrollment', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  courseId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Courses',
      key: 'id',
    },
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Users',
      key: 'id',
    },
  },
  enrolledAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
  startedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Cuando el usuario comenzó realmente el curso',
  },
  completedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Cuando el usuario completó el curso',
  },
  lastAccessedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Último acceso al curso',
  },
  status: {
    type: DataTypes.ENUM('enrolled', 'in_progress', 'completed', 'dropped', 'suspended'),
    allowNull: false,
    defaultValue: 'enrolled',
  },
  progressPercentage: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: false,
    defaultValue: 0.00,
    comment: 'Porcentaje de progreso en el curso',
  },
  currentModuleId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'Modules',
      key: 'id',
    },
    comment: 'Módulo actual del estudiante',
  },
  currentLessonId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'Lessons',
      key: 'id',
    },
    comment: 'Lección actual del estudiante',
  },
  totalTimeSpent: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: 'Tiempo total empleado en minutos',
  },
  certificateIssued: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
  },
  certificateIssuedAt: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  finalScore: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true,
    comment: 'Puntuación final del curso',
  },
  enrolledById: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'Users',
      key: 'id',
    },
    comment: 'Usuario que inscribió (si fue inscripción manual)',
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Notas adicionales sobre la inscripción',
  },
}, {
  timestamps: true,
  indexes: [
    {
      fields: ['courseId'],
    },
    {
      fields: ['userId'],
    },
    {
      fields: ['courseId', 'userId'],
      unique: true,
    },
    {
      fields: ['status'],
    },
    {
      fields: ['enrolledAt'],
    },
    {
      fields: ['completedAt'],
    },
  ],
});

module.exports = Enrollment;
